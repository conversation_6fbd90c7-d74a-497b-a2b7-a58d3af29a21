import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, FindOptionsSelect } from 'typeorm';
import { JobStage } from 'src/entities/job-stage.entity';
import { JobStageCompletion, STAGE_COMPLETION_STATUS, COMPLETED_BY_TYPE } from 'src/entities/job-stage-completion.entity';
import { Job } from 'src/entities/job.entity';
import { Stages } from 'src/entities/stage.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { Organization } from 'src/entities/organization.entity';
import { User } from 'src/entities/user.entity';
import { CreateJobStageDto, CompleteStageDto, UpdateStageCompletionDto } from 'src/dto/job-stage.dto';

@Injectable()
export class JobStageService {
  constructor(
    @InjectRepository(JobStage, 'mysql')
    private jobStageRepository: Repository<JobStage>,
    @InjectRepository(JobStageCompletion, 'mysql')
    private jobStageCompletionRepository: Repository<JobStageCompletion>,
    @InjectRepository(Job, 'mysql')
    private jobRepository: Repository<Job>,
    @InjectRepository(Stages, 'mysql')
    private stagesRepository: Repository<Stages>,
    @InjectRepository(OnboardingEmployee, 'mysql')
    private onboardingEmployeeRepository: Repository<OnboardingEmployee>,
  ) {}

  public select: FindOptionsSelect<JobStage> = {
    id: false,
    job_stage_id: true,
    sequence_order: true,
    is_required: true,
    is_active: true,
    created_at: true,
    updated_at: false,
    deleted_at: false,
  };

  public completionSelect: FindOptionsSelect<JobStageCompletion> = {
    id: false,
    completion_id: true,
    status: true,
    started_at: true,
    completed_at: true,
    completed_by_type: true,
    completion_notes: true,
    completion_data: true,
    created_at: true,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Create Job Stage
   * @param job Job
   * @param createJobStageDto CreateJobStageDto
   * @param organization Organization
   * @returns Promise<JobStage>
   */
  async createJobStage(
    job: Job,
    createJobStageDto: CreateJobStageDto,
    organization: Organization,
  ): Promise<JobStage> {
    try {
      // Find the stage
      const stage = await this.stagesRepository.findOne({
        where: { stage_id: createJobStageDto.stage_id },
      });

      if (!stage) {
        throw new HttpException('Stage not found', HttpStatus.NOT_FOUND);
      }

      const jobStage = this.jobStageRepository.create({
        job,
        stage,
        sequence_order: createJobStageDto.sequence_order || 1,
        is_required: createJobStageDto.is_required ?? true,
        is_active: createJobStageDto.is_active ?? true,
        organization,
      });

      return await this.jobStageRepository.save(jobStage);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Assign multiple stages to a job
   * @param job Job
   * @param stages CreateJobStageDto[]
   * @param organization Organization
   * @returns Promise<JobStage[]>
   */
  async assignStagesToJob(
    job: Job,
    stages: CreateJobStageDto[],
    organization: Organization,
  ): Promise<JobStage[]> {
    try {
      const jobStages: JobStage[] = [];

      for (const stageDto of stages) {
        const jobStage = await this.createJobStage(job, stageDto, organization);
        jobStages.push(jobStage);
      }

      return jobStages;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get job stages for a job
   * @param job_id string
   * @returns Promise<JobStage[]>
   */
  async getJobStages(job_id: string): Promise<JobStage[]> {
    try {
      return await this.jobStageRepository.find({
        where: { job: { job_id } },
        select: this.select,
        relations: {
          stage: true,
          job: true,
        },
        order: {
          sequence_order: 'ASC',
        },
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Initialize stage completions for an onboarding employee
   * @param onboardingEmployee OnboardingEmployee
   * @returns Promise<JobStageCompletion[]>
   */
  async initializeStageCompletions(
    onboardingEmployee: OnboardingEmployee,
  ): Promise<JobStageCompletion[]> {
    try {
      // Get job stages for the employee's job
      const jobStages = await this.jobStageRepository.find({
        where: { 
          job: { id: onboardingEmployee.job.id },
          is_active: true,
        },
        relations: { job: true, organization: true },
        order: { sequence_order: 'ASC' },
      });

      const completions: JobStageCompletion[] = [];

      for (const jobStage of jobStages) {
        // Check if completion already exists
        const existingCompletion = await this.jobStageCompletionRepository.findOne({
          where: {
            job_stage: { id: jobStage.id },
            onboarding_employee: { id: onboardingEmployee.id },
          },
        });

        if (!existingCompletion) {
          const completion = this.jobStageCompletionRepository.create({
            job_stage: jobStage,
            onboarding_employee,
            status: STAGE_COMPLETION_STATUS.NOT_STARTED,
            organization: jobStage.organization,
          });

          const savedCompletion = await this.jobStageCompletionRepository.save(completion);
          completions.push(savedCompletion);
        }
      }

      return completions;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Complete a stage
   * @param completeStageDto CompleteStageDto
   * @param completedByUser User
   * @returns Promise<JobStageCompletion>
   */
  async completeStage(
    completeStageDto: CompleteStageDto,
    completedByUser?: User,
  ): Promise<JobStageCompletion> {
    try {
      const completion = await this.jobStageCompletionRepository.findOne({
        where: {
          job_stage: { job_stage_id: completeStageDto.job_stage_id },
          onboarding_employee: { onboarding_employee_id: completeStageDto.onboarding_employee_id },
        },
        relations: {
          job_stage: true,
          onboarding_employee: true,
          organization: true,
        },
      });

      if (!completion) {
        throw new HttpException('Stage completion not found', HttpStatus.NOT_FOUND);
      }

      // Update completion
      completion.status = completeStageDto.status || STAGE_COMPLETION_STATUS.COMPLETED;
      completion.completed_at = new Date();
      completion.completed_by_user = completedByUser;
      completion.completed_by_type = completeStageDto.completed_by_type || COMPLETED_BY_TYPE.USER;
      completion.completion_notes = completeStageDto.completion_notes;
      completion.completion_data = completeStageDto.completion_data;

      if (completion.status === STAGE_COMPLETION_STATUS.IN_PROGRESS && !completion.started_at) {
        completion.started_at = new Date();
      }

      return await this.jobStageCompletionRepository.save(completion);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get stage completion progress for an employee
   * @param onboarding_employee_id string
   * @returns Promise<JobStageCompletion[]>
   */
  async getStageProgress(onboarding_employee_id: string): Promise<JobStageCompletion[]> {
    try {
      return await this.jobStageCompletionRepository.find({
        where: {
          onboarding_employee: { onboarding_employee_id },
        },
        select: this.completionSelect,
        relations: {
          job_stage: {
            stage: true,
          },
          completed_by_user: true,
        },
        order: {
          job_stage: {
            sequence_order: 'ASC',
          },
        },
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update stage completion
   * @param completion_id string
   * @param updateDto UpdateStageCompletionDto
   * @returns Promise<JobStageCompletion>
   */
  async updateStageCompletion(
    completion_id: string,
    updateDto: UpdateStageCompletionDto,
  ): Promise<JobStageCompletion> {
    try {
      const completion = await this.jobStageCompletionRepository.findOne({
        where: { completion_id },
      });

      if (!completion) {
        throw new HttpException('Stage completion not found', HttpStatus.NOT_FOUND);
      }

      Object.assign(completion, updateDto);

      return await this.jobStageCompletionRepository.save(completion);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
