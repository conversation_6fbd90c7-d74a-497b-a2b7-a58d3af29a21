import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, FindOptionsSelect, In } from 'typeorm';
import {
  JobStageCompletion,
  STAGE_COMPLETION_STATUS,
  COMPLETED_BY_TYPE,
} from 'src/entities/job-stage-completion.entity';
import { Job } from 'src/entities/job.entity';
import { Stages } from 'src/entities/stage.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { Organization } from 'src/entities/organization.entity';
import { User } from 'src/entities/user.entity';
import {
  AssignJobStagesDto,
  CompleteStageDto,
  UpdateStageCompletionDto,
} from 'src/dto/job-stage.dto';

@Injectable()
export class JobStageService {
  constructor(
    @InjectRepository(JobStageCompletion, 'mysql')
    private jobStageCompletionRepository: Repository<JobStageCompletion>,
    @InjectRepository(Job, 'mysql')
    private jobRepository: Repository<Job>,
    @InjectRepository(Stages, 'mysql')
    private stagesRepository: Repository<Stages>,
    @InjectRepository(OnboardingEmployee, 'mysql')
    private onboardingEmployeeRepository: Repository<OnboardingEmployee>,
  ) {}

  public completionSelect: FindOptionsSelect<JobStageCompletion> = {
    id: false,
    completion_id: true,
    status: true,
    started_at: true,
    completed_at: true,
    completed_by_type: true,
    completion_notes: true,
    completion_data: true,
    created_at: true,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Assign stages to a job
   * @param job Job
   * @param stage_ids string[]
   * @returns Promise<Job>
   */
  async assignStagesToJob(job: Job, stage_ids: string[]): Promise<Job> {
    try {
      // Find the stages
      const stages = await this.stagesRepository.find({
        where: { stage_id: In(stage_ids) },
      });

      if (stages.length !== stage_ids.length) {
        throw new HttpException('Some stages not found', HttpStatus.NOT_FOUND);
      }

      // Assign stages to job
      job.stages = stages;
      return await this.jobRepository.save(job);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get job stages for a job
   * @param job_id string
   * @returns Promise<Stages[]>
   */
  async getJobStages(job_id: string): Promise<Stages[]> {
    try {
      const job = await this.jobRepository.findOne({
        where: { job_id },
        relations: { stages: true },
      });

      if (!job) {
        throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
      }

      return job.stages || [];
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Initialize stage completions for an onboarding employee
   * @param onboardingEmployee OnboardingEmployee
   * @returns Promise<JobStageCompletion[]>
   */
  async initializeStageCompletions(
    onboardingEmployee: OnboardingEmployee,
  ): Promise<JobStageCompletion[]> {
    try {
      // Get job with stages for the employee's job
      const job = await this.jobRepository.findOne({
        where: { id: onboardingEmployee.job.id },
        relations: { stages: true, organization: true },
      });

      if (!job || !job.stages || job.stages.length === 0) {
        return [];
      }

      const completions: JobStageCompletion[] = [];

      for (let i = 0; i < job.stages.length; i++) {
        const stage = job.stages[i];

        // Check if completion already exists
        const existingCompletion =
          await this.jobStageCompletionRepository.findOne({
            where: {
              job: { id: job.id },
              stage: { id: stage.id },
              onboarding_employee: { id: onboardingEmployee.id },
            },
          });

        if (!existingCompletion) {
          const completion = this.jobStageCompletionRepository.create({
            job: job,
            stage: stage,
            onboarding_employee: onboardingEmployee,
            sequence_order: i + 1,
            status: STAGE_COMPLETION_STATUS.NOT_STARTED,
            organization: job.organization,
          });

          const savedCompletion = await this.jobStageCompletionRepository.save(
            completion,
          );
          completions.push(savedCompletion);
        }
      }

      return completions;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Complete a stage
   * @param completeStageDto CompleteStageDto
   * @param completedByUser User
   * @returns Promise<JobStageCompletion>
   */
  async completeStage(
    completeStageDto: CompleteStageDto,
    completedByUser?: User,
  ): Promise<JobStageCompletion> {
    try {
      const completion = await this.jobStageCompletionRepository.findOne({
        where: {
          job: { job_id: completeStageDto.job_id },
          stage: { stage_id: completeStageDto.stage_id },
          onboarding_employee: {
            onboarding_employee_id: completeStageDto.onboarding_employee_id,
          },
        },
        relations: {
          job: true,
          stage: true,
          onboarding_employee: true,
          organization: true,
        },
      });

      if (!completion) {
        throw new HttpException(
          'Stage completion not found',
          HttpStatus.NOT_FOUND,
        );
      }

      // Update completion
      completion.status =
        completeStageDto.status || STAGE_COMPLETION_STATUS.COMPLETED;
      completion.completed_at = new Date();
      completion.completed_by_user = completedByUser;
      completion.completed_by_type =
        completeStageDto.completed_by_type || COMPLETED_BY_TYPE.USER;
      completion.completion_notes = completeStageDto.completion_notes;
      completion.completion_data = completeStageDto.completion_data;

      if (
        completion.status === STAGE_COMPLETION_STATUS.IN_PROGRESS &&
        !completion.started_at
      ) {
        completion.started_at = new Date();
      }

      return await this.jobStageCompletionRepository.save(completion);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get stage completion progress for an employee
   * @param onboarding_employee_id string
   * @returns Promise<JobStageCompletion[]>
   */
  async getStageProgress(
    onboarding_employee_id: string,
  ): Promise<JobStageCompletion[]> {
    try {
      return await this.jobStageCompletionRepository.find({
        where: {
          onboarding_employee: { onboarding_employee_id },
        },
        select: this.completionSelect,
        relations: {
          job: true,
          stage: true,
          completed_by_user: true,
        },
        order: {
          sequence_order: 'ASC',
        },
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update stage completion
   * @param completion_id string
   * @param updateDto UpdateStageCompletionDto
   * @returns Promise<JobStageCompletion>
   */
  async updateStageCompletion(
    completion_id: string,
    updateDto: UpdateStageCompletionDto,
  ): Promise<JobStageCompletion> {
    try {
      const completion = await this.jobStageCompletionRepository.findOne({
        where: { completion_id },
      });

      if (!completion) {
        throw new HttpException(
          'Stage completion not found',
          HttpStatus.NOT_FOUND,
        );
      }

      Object.assign(completion, updateDto);

      return await this.jobStageCompletionRepository.save(completion);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
