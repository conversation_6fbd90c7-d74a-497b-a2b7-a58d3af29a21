import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import OrganizationController from './organization.controller';
import { OrganizationService } from 'src/services/organization.service';
import { Organization } from 'src/entities/organization.entity';
import {
  OrganizationExistsRule,
  OrganizationMailExistsRule,
  OrganizationMobileExistsRule,
  OrganizationNameExistsRule,
} from 'src/util/validations/organization.validation';
import { AdminModule } from '../admin/admin.module';
import EncryptionService from 'src/services/encryption.service';
import { FormsRepositoryModule } from '../formsrepository/formsrepository.module';
import { MongooseModule } from '@nestjs/mongoose';
import {
  OrgFormsRepository,
  OrgFormsRepositorySchema,
} from 'src/entities/mongodb/orgformsrepository.entity';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { AppsModule } from '../apps/apps.module';
import {
  FormsRepository,
  FormsRepositorySchema,
} from 'src/entities/mongodb/formsrepository.entity';
import {
  FormValuesRepository,
  FormValuesRepositorySchema,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import { GlobalService } from 'src/services/global.service';
import {
  FormFields,
  FormFieldsSchema,
} from 'src/entities/mongodb/formfields.entity';
import { FormFieldsModule } from '../formfields/formfields.module';
import {
  ClientsRepository,
  ClientsRepositorySchema,
} from 'src/entities/mongodb/clientsrepository.entity';
import { OrgFormsExistRule } from 'src/util/validations/forms.validations';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { OrgAppConfigurationService } from 'src/services/org-app-configuration.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { SendGridService } from 'src/util/sendgrid.service';
import {
  ESignRequestLogsRepository,
  ESignRequestLogsSchema,
} from 'src/entities/mongodb/e-sign-request-log.entity';
import { ConfigurationsService } from 'src/services/configurations.service';
import { Configurations } from 'src/entities/configurations.entity';
import { HttpModule } from '@nestjs/axios';
import {
  PrimaryFormsRepository,
  PrimaryFormsRepositorySchema,
} from 'src/entities/mongodb/primary-forms-repository.entity';
import {
  AppOrgFormsToggleRepository,
  AppOrgFormsToggleRepositorySchema,
} from 'src/entities/mongodb/app-org-forms-toggle.entity';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { User } from 'src/entities/user.entity';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { FormValueService } from 'src/services/formvalues.service';
import { UserService } from 'src/services/user.service';
import { PaginationService } from 'src/util/pagination.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import { Caregivers } from 'src/entities/caregivers.entity';
import { CaregiversService } from 'src/services/caregivers.service';
import { JobModule } from '../job/job.module';
import { Job } from 'src/entities/job.entity';
import { JobVersion } from 'src/entities/job-version.entity';
import { JobStage } from 'src/entities/job-stage.entity';
import { JobStageCompletion } from 'src/entities/job-stage-completion.entity';
import { Stages } from 'src/entities/stage.entity';

@Module({
  imports: [
    forwardRef(() => AppsModule),
    AdminModule,
    forwardRef(() => FormsRepositoryModule),
    TypeOrmModule.forFeature(
      [
        Organization,
        IndustryTypes,
        OrgAppConfiguration,
        Configurations,
        OnboardingEmployee,
        User,
        IndustryAppProcess,
        Caregivers,
        Job,
        JobVersion,
        JobStage,
        JobStageCompletion,
        Stages,
      ],
      'mysql',
    ),
    MongooseModule.forFeature([
      { name: OrgFormsRepository.name, schema: OrgFormsRepositorySchema },
      { name: FormsRepository.name, schema: FormsRepositorySchema },
      { name: FormFields.name, schema: FormFieldsSchema },
      { name: FormValuesRepository.name, schema: FormValuesRepositorySchema },
      { name: ClientsRepository.name, schema: ClientsRepositorySchema },
      { name: ESignRequestLogsRepository.name, schema: ESignRequestLogsSchema },
      {
        name: PrimaryFormsRepository.name,
        schema: PrimaryFormsRepositorySchema,
      },
      {
        name: AppOrgFormsToggleRepository.name,
        schema: AppOrgFormsToggleRepositorySchema,
      },
      {
        name: OnBoardEmpChecklistRepository.name,
        schema: OnBoardEmpChecklistRepositorySchema,
      },
      {
        name: OnBoardOrgChecklistRepository.name,
        schema: OnBoardOrgChecklistRepositorySchema,
      },
    ]),
    FormFieldsModule,
    HttpModule,
    forwardRef(() => JobModule),
  ],
  controllers: [OrganizationController],
  providers: [
    GlobalService,
    OrgFormsExistRule,
    EncryptionService,
    OrganizationService,
    OrganizationExistsRule,
    OrgFormsRepositoryService,
    OrganizationMailExistsRule,
    OrganizationMobileExistsRule,
    OrganizationNameExistsRule,
    IndustryTypesService,
    OrgAppConfigurationService,
    ClientsRepositoryService,
    SendGridService,
    ConfigurationsService,
    OnboardingEmployeeService,
    FormValueService,
    UserService,
    PaginationService,
    CaregiversService,
    OnBoardEmpChecklistService,
    OnBoardOrgChecklistService,
  ],
  exports: [
    SendGridService,
    EncryptionService,
    OrganizationService,
    IndustryTypesService,
    OrgFormsRepositoryService,
    MongooseModule.forFeature([
      { name: OrgFormsRepository.name, schema: OrgFormsRepositorySchema },
      { name: FormsRepository.name, schema: FormsRepositorySchema },
      { name: FormFields.name, schema: FormFieldsSchema },
      { name: FormValuesRepository.name, schema: FormValuesRepositorySchema },
      { name: ClientsRepository.name, schema: ClientsRepositorySchema },
    ]),
  ],
})
export class OrganizationModule {}
