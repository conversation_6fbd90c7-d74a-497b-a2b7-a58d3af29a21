# Job Stage V2 Implementation Guide

## Overview

This guide helps you implement the enhanced V2 job stage system while maintaining your existing V1 flow. The V2 system provides sophisticated automation and manual controls for your complex onboarding process.

## Implementation Steps

### 1. Database Migration

First, run the database migration to add the new V2 fields:

```sql
-- Add V2 fields to job_stage_completion table
ALTER TABLE job_stage_completion 
ADD COLUMN form_scores JSON NULL,
ADD COLUMN scheduled_data JSON NULL,
ADD COLUMN documents JSON NULL,
ADD COLUMN admin_notes TEXT NULL,
ADD COLUMN rejection_reason TEXT NULL,
ADD COLUMN requires_admin_action BOOLEAN DEFAULT FALSE,
ADD COLUMN auto_advance BOOLEAN DEFAULT FALSE,
ADD COLUMN notification_data JSON NULL,
ADD COLUMN scheduled_date DATETIME NULL,
ADD COLUMN due_date DATETIME NULL;

-- Add V2 fields to job_stage table
ALTER TABLE job_stage 
ADD COLUMN stage_type ENUM('automatic', 'manual', 'hybrid') DEFAULT 'automatic',
ADD COLUMN associated_forms JSON NULL,
ADD COLUMN requires_scheduling BOOLEAN DEFAULT FALSE,
ADD COLUMN requires_documents BOOLEAN DEFAULT FALSE,
ADD COLUMN requires_admin_approval BOOLEAN DEFAULT FALSE,
ADD COLUMN auto_advance_conditions JSON NULL,
ADD COLUMN notification_templates JSON NULL,
ADD COLUMN min_score_required INT NULL,
ADD COLUMN max_duration_days INT NULL;

-- Update stage_completion_status enum to include V2 statuses
ALTER TABLE job_stage_completion 
MODIFY COLUMN status ENUM(
  'Not Started', 'In Progress', 'Completed', 'Skipped',
  'Scheduled', 'Re-Scheduled', 'Pending Approval', 
  'Approved', 'Rejected', 'Waiting for Documents'
) DEFAULT 'Not Started';
```

### 2. Configure Your Stages

#### Example: Application Stage (Automatic)
```typescript
const applicationStage = {
  title: 'Application Review',
  stage_type: 'automatic',
  associated_forms: [
    {
      form_id: 'personal-information',
      form_type: 'normal',
      is_required: true
    },
    {
      form_id: 'employment-details',
      form_type: 'normal',
      is_required: true
    }
  ],
  auto_advance_conditions: {
    all_forms_completed: true
  },
  notification_templates: {
    started: 'application_started_template',
    completed: 'application_completed_template'
  }
};
```

#### Example: Interview Stage (Manual)
```typescript
const interviewStage = {
  title: 'Technical Interview',
  stage_type: 'manual',
  requires_scheduling: true,
  requires_admin_approval: true,
  max_duration_days: 7,
  notification_templates: {
    scheduled: 'interview_scheduled_template',
    reminder: 'interview_reminder_template',
    completed: 'interview_completed_template'
  }
};
```

#### Example: Orientation Stage (Hybrid)
```typescript
const orientationStage = {
  title: 'Orientation Process',
  stage_type: 'hybrid',
  requires_admin_approval: true, // Admin must start
  associated_forms: [
    {
      form_id: 'company-policies-quiz',
      form_type: 'quiz',
      is_required: true,
      min_score: 80
    },
    {
      form_id: 'safety-training-quiz',
      form_type: 'quiz',
      is_required: true,
      min_score: 90
    }
  ],
  min_score_required: 85, // Overall stage minimum
  auto_advance_conditions: {
    all_quiz_forms_completed: true,
    min_score_achieved: true
  }
};
```

### 3. Integration Points

#### Form Submission Integration
Update your existing form submission handler:

```typescript
// In your existing form submission endpoint
@Post('forms/:form_id')
async saveFormValues(
  @Body() body: any,
  @Param('form_id') form_id: string,
  @Req() request: Request,
) {
  // Your existing V1 logic
  const v1Result = await this.existingFormSubmissionLogic(body, form_id, request);
  
  // Add V2 enhancement
  if (this.isV2Enabled(request.user['organization_id'])) {
    await this.jobStageV2Service.handleFormSubmissionV2(
      form_id,
      request.user['user_id'],
      body,
      request.user['organization_id']
    );
  }
  
  return v1Result;
}
```

#### Employee Status Integration
Map V1 statuses to V2 stage completions:

```typescript
// Helper function to sync V1 and V2 statuses
async syncV1ToV2Status(employee: OnboardingEmployee) {
  const stageProgress = await this.jobStageV2Service.getStageProgress(
    employee.onboarding_employee_id
  );
  
  switch (employee.status) {
    case EMPLOYEE_STATUS.APPLICATION_COMPLETED:
      await this.completeStageByTitle(stageProgress, 'Application Review');
      break;
    case EMPLOYEE_STATUS.INTERVIEW_SCHEDULED:
      await this.updateStageStatus(stageProgress, 'Interview', 'SCHEDULED');
      break;
    case EMPLOYEE_STATUS.INTERVIEW_COMPLETED:
      await this.completeStageByTitle(stageProgress, 'Interview');
      break;
    // ... other mappings
  }
}
```

### 4. Frontend Integration

#### Enhanced Progress Display
```typescript
// Frontend component for V2 progress
interface StageProgressV2 {
  stage: {
    title: string;
    stage_type: 'automatic' | 'manual' | 'hybrid';
    sequence_order: number;
  };
  status: string;
  stage_metadata: {
    is_manual: boolean;
    requires_admin: boolean;
    has_forms: boolean;
    requires_scheduling: boolean;
  };
  completion_metadata: {
    has_scores: boolean;
    is_scheduled: boolean;
    needs_admin_action: boolean;
  };
  form_scores?: any;
  scheduled_data?: any;
}

// Usage in React component
const StageProgressComponent = ({ employeeId }) => {
  const [progress, setProgress] = useState<StageProgressV2[]>([]);
  
  useEffect(() => {
    fetch(`/job-stages/v2/progress/${employeeId}`)
      .then(res => res.json())
      .then(data => setProgress(data.data));
  }, [employeeId]);
  
  return (
    <div className="stage-progress">
      {progress.map((stage, index) => (
        <StageCard 
          key={index} 
          stage={stage} 
          isV2Enhanced={true}
        />
      ))}
    </div>
  );
};
```

#### Admin Action Dashboard
```typescript
// Admin dashboard for pending actions
const AdminDashboard = () => {
  const [pendingActions, setPendingActions] = useState([]);
  
  useEffect(() => {
    fetch('/job-stages/v2/admin-actions?status=PENDING_APPROVAL')
      .then(res => res.json())
      .then(data => setPendingActions(data.data));
  }, []);
  
  return (
    <div className="admin-dashboard">
      <h2>Pending Actions</h2>
      {pendingActions.map(action => (
        <ActionCard 
          key={action.id} 
          action={action}
          onComplete={handleActionComplete}
        />
      ))}
    </div>
  );
};
```

### 5. Email Template Configuration

#### V2 Email Templates
```typescript
// Email templates for V2 system
const emailTemplates = {
  stage_started: {
    subject: 'Next Step: {{stage_title}}',
    body: `
      Dear {{employee_name}},
      
      You can now proceed with the {{stage_title}} stage.
      {{#if has_forms}}
      Please complete the following forms:
      {{#each forms}}
      - {{name}}
      {{/each}}
      {{/if}}
      
      Login to continue: {{login_url}}
    `
  },
  
  interview_scheduled: {
    subject: 'Interview Scheduled - {{job_title}}',
    body: `
      Dear {{employee_name}},
      
      Your interview has been scheduled:
      
      Date: {{scheduled_date}}
      Time: {{scheduled_time}}
      Location: {{location}}
      Interviewer: {{interviewer_name}}
      
      {{#if notes}}
      Additional Notes: {{notes}}
      {{/if}}
    `
  },
  
  quiz_results: {
    subject: 'Quiz Results - {{stage_title}}',
    body: `
      Dear {{employee_name}},
      
      Your quiz results for {{stage_title}}:
      
      Score: {{scored_points}}/{{total_points}} ({{percentage}}%)
      
      {{#if passed}}
      Congratulations! You have successfully completed this stage.
      {{else}}
      Unfortunately, you did not meet the minimum score requirement.
      Please contact your administrator for next steps.
      {{/if}}
    `
  }
};
```

### 6. Testing Strategy

#### Unit Tests
```typescript
describe('JobStageV2Service', () => {
  it('should auto-advance after form completion', async () => {
    const result = await service.handleFormSubmissionV2(
      'form-id',
      'user-id',
      formData,
      'org-id'
    );
    
    expect(result.stage_updated).toBe(true);
    expect(result.auto_advanced).toBe(true);
  });
  
  it('should require admin action for manual stages', async () => {
    const completion = await service.completeStageV2({
      job_stage_id: 'manual-stage-id',
      status: 'PENDING_APPROVAL',
      requires_admin_action: true
    });
    
    expect(completion.requires_admin_action).toBe(true);
  });
});
```

#### Integration Tests
```typescript
describe('V2 Flow Integration', () => {
  it('should complete full onboarding flow', async () => {
    // 1. Submit application forms
    await submitApplicationForms();
    
    // 2. Schedule interview
    await scheduleInterview();
    
    // 3. Complete interview
    await completeInterview(true);
    
    // 4. Start orientation
    await startOrientation();
    
    // 5. Complete quiz forms
    await submitQuizForms();
    
    // 6. Upload BGV documents
    await uploadBGVDocuments();
    
    // 7. Verify employee conversion
    const employee = await getEmployee();
    expect(employee.status).toBe('EMPLOYEE_ONBOARD_COMPLETED');
  });
});
```

### 7. Rollout Plan

#### Phase 1: Preparation (Week 1)
- [ ] Run database migrations
- [ ] Deploy V2 services (inactive)
- [ ] Configure email templates
- [ ] Set up monitoring

#### Phase 2: Pilot (Week 2)
- [ ] Enable V2 for test organization
- [ ] Test all flows thoroughly
- [ ] Gather feedback
- [ ] Fix any issues

#### Phase 3: Gradual Rollout (Weeks 3-4)
- [ ] Enable V2 for 25% of organizations
- [ ] Monitor performance and errors
- [ ] Enable for 50% of organizations
- [ ] Enable for 75% of organizations

#### Phase 4: Full Deployment (Week 5)
- [ ] Enable V2 for all organizations
- [ ] Monitor system performance
- [ ] Provide user training
- [ ] Document lessons learned

### 8. Monitoring and Analytics

#### Key Metrics to Track
- Stage completion times
- Admin intervention frequency
- Quiz pass/fail rates
- Email delivery rates
- System performance metrics

#### Dashboard Queries
```sql
-- Average time per stage
SELECT 
  js.title,
  AVG(TIMESTAMPDIFF(HOUR, jsc.started_at, jsc.completed_at)) as avg_hours
FROM job_stage_completion jsc
JOIN job_stage js ON jsc.job_stage_id = js.id
WHERE jsc.status = 'COMPLETED'
GROUP BY js.title;

-- Admin action frequency
SELECT 
  COUNT(*) as pending_actions,
  js.title
FROM job_stage_completion jsc
JOIN job_stage js ON jsc.job_stage_id = js.id
WHERE jsc.requires_admin_action = true
  AND jsc.status = 'PENDING_APPROVAL'
GROUP BY js.title;
```

This implementation guide ensures a smooth transition to the V2 system while preserving your existing V1 functionality! 🚀
