import {
  <PERSON><PERSON>num,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
  IsDateString,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { EMPLOYMENT_TYPE, JOB_STATUS } from 'src/entities/job.entity';
import { CreateJobStageDto } from './job-stage.dto';

export class CreateJobDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  department: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  requirements: string;

  @IsOptional()
  @IsString()
  salary_range: string;

  @IsOptional()
  @IsEnum(EMPLOYMENT_TYPE)
  employment_type: EMPLOYMENT_TYPE;

  @IsOptional()
  @IsString()
  location: string;

  @IsOptional()
  @IsEnum(JOB_STATUS)
  status: JOB_STATUS;

  @IsOptional()
  @IsDateString()
  application_start_date: string;

  @IsOptional()
  @IsDateString()
  application_end_date: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateJobStageDto)
  stages?: CreateJobStageDto[];
}

export class UpdateJobDto {
  @IsOptional()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  department: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  requirements: string;

  @IsOptional()
  @IsString()
  salary_range: string;

  @IsOptional()
  @IsEnum(EMPLOYMENT_TYPE)
  employment_type: EMPLOYMENT_TYPE;

  @IsOptional()
  @IsString()
  location: string;

  @IsOptional()
  @IsEnum(JOB_STATUS)
  status: JOB_STATUS;

  @IsOptional()
  @IsDateString()
  application_start_date: string;

  @IsOptional()
  @IsDateString()
  application_end_date: string;

  @IsOptional()
  @IsString()
  change_reason: string;
}
