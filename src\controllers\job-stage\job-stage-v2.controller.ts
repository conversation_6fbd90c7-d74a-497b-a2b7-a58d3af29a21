import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Req,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Query,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthGuard } from '@nestjs/passport';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { 
  JobStageV2Service, 
  StageCompletionV2Dto, 
  ScheduleInterviewDto, 
  UploadDocumentDto 
} from 'src/services/job-stage-v2.service';
import { STAGE_COMPLETION_STATUS, COMPLETED_BY_TYPE } from 'src/entities/job-stage-completion.entity';

// V2 DTOs
export class FormSubmissionV2Dto {
  form_id: string;
  user_id: string;
  form_data: any;
}

export class CompleteInterviewV2Dto {
  job_stage_id: string;
  onboarding_employee_id: string;
  admin_notes: string;
  passed: boolean;
}

export class StartOrientationV2Dto {
  onboarding_employee_id: string;
  admin_notes?: string;
}

@Controller('job-stages/v2')
@UseGuards(AuthGuard('jwt'), OrganizationAuth)
export default class JobStageV2Controller {
  constructor(private readonly jobStageV2Service: JobStageV2Service) {}

  /**
   * V2: Handle form submission with automatic stage progression
   * @param req Request
   * @param res Response
   * @param formSubmissionDto FormSubmissionV2Dto
   */
  @Post('form-submission')
  @UsePipes(new ValidationPipe())
  async handleFormSubmissionV2(
    @Req() req: Request,
    @Res() res: Response,
    @Body() formSubmissionDto: FormSubmissionV2Dto,
  ) {
    try {
      const result = await this.jobStageV2Service.handleFormSubmissionV2(
        formSubmissionDto.form_id,
        formSubmissionDto.user_id,
        formSubmissionDto.form_data,
        req.user['organization_id'],
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Form submission processed successfully',
        data: result,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Schedule interview with enhanced data
   * @param req Request
   * @param res Response
   * @param scheduleDto ScheduleInterviewDto
   */
  @Post('schedule-interview')
  @UsePipes(new ValidationPipe())
  async scheduleInterviewV2(
    @Req() req: Request,
    @Res() res: Response,
    @Body() scheduleDto: ScheduleInterviewDto,
  ) {
    try {
      const completion = await this.jobStageV2Service.scheduleInterviewV2(
        scheduleDto,
        req.user,
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Interview scheduled successfully',
        data: completion,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Re-schedule interview
   * @param req Request
   * @param res Response
   * @param scheduleDto ScheduleInterviewDto
   */
  @Put('reschedule-interview')
  @UsePipes(new ValidationPipe())
  async rescheduleInterviewV2(
    @Req() req: Request,
    @Res() res: Response,
    @Body() scheduleDto: ScheduleInterviewDto,
  ) {
    try {
      // First update the status to re-scheduled
      await this.jobStageV2Service.completeStageV2({
        job_stage_id: scheduleDto.job_stage_id,
        onboarding_employee_id: scheduleDto.onboarding_employee_id,
        status: STAGE_COMPLETION_STATUS.RE_SCHEDULED,
        completed_by_type: COMPLETED_BY_TYPE.ADMIN,
        completion_notes: 'Interview re-scheduled',
      });

      // Then schedule the new interview
      const completion = await this.jobStageV2Service.scheduleInterviewV2(
        scheduleDto,
        req.user,
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Interview re-scheduled successfully',
        data: completion,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Complete interview
   * @param req Request
   * @param res Response
   * @param completeDto CompleteInterviewV2Dto
   */
  @Post('complete-interview')
  @UsePipes(new ValidationPipe())
  async completeInterviewV2(
    @Req() req: Request,
    @Res() res: Response,
    @Body() completeDto: CompleteInterviewV2Dto,
  ) {
    try {
      const completion = await this.jobStageV2Service.completeInterviewV2(
        completeDto.job_stage_id,
        completeDto.onboarding_employee_id,
        completeDto.admin_notes,
        completeDto.passed,
        req.user,
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        message: completeDto.passed 
          ? 'Interview completed successfully' 
          : 'Interview completed - candidate not selected',
        data: completion,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Start orientation process
   * @param req Request
   * @param res Response
   * @param startDto StartOrientationV2Dto
   */
  @Post('start-orientation')
  @UsePipes(new ValidationPipe())
  async startOrientationV2(
    @Req() req: Request,
    @Res() res: Response,
    @Body() startDto: StartOrientationV2Dto,
  ) {
    try {
      // Find the orientation stage for this employee
      const stageProgress = await this.jobStageV2Service.getStageProgress(
        startDto.onboarding_employee_id,
      );

      const orientationStage = stageProgress.find(
        completion => completion.job_stage.title.toLowerCase().includes('orientation')
      );

      if (!orientationStage) {
        throw new HttpException('Orientation stage not found', HttpStatus.NOT_FOUND);
      }

      const completion = await this.jobStageV2Service.completeStageV2({
        job_stage_id: orientationStage.job_stage.job_stage_id,
        onboarding_employee_id: startDto.onboarding_employee_id,
        status: STAGE_COMPLETION_STATUS.IN_PROGRESS,
        completed_by_type: COMPLETED_BY_TYPE.ADMIN,
        completion_notes: 'Orientation started by admin',
        admin_notes: startDto.admin_notes,
      });

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Orientation started successfully',
        data: completion,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Upload background verification documents
   * @param req Request
   * @param res Response
   * @param uploadDto UploadDocumentDto
   */
  @Post('upload-bgv-documents')
  @UsePipes(new ValidationPipe())
  async uploadBGVDocumentsV2(
    @Req() req: Request,
    @Res() res: Response,
    @Body() uploadDto: UploadDocumentDto,
  ) {
    try {
      const completion = await this.jobStageV2Service.uploadBGVDocumentsV2(
        uploadDto,
        req.user,
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Background verification documents uploaded successfully',
        data: completion,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Get enhanced stage progress
   * @param req Request
   * @param res Response
   * @param onboarding_employee_id string
   */
  @Get('progress/:onboarding_employee_id')
  async getStageProgressV2(
    @Req() req: Request,
    @Res() res: Response,
    @Param('onboarding_employee_id') onboarding_employee_id: string,
  ) {
    try {
      const progress = await this.jobStageV2Service.getStageProgress(
        onboarding_employee_id,
      );

      // Enhanced progress with additional metadata
      const enhancedProgress = progress.map(completion => ({
        ...completion,
        stage_metadata: {
          is_manual: completion.job_stage.stage_type === 'manual',
          requires_admin: completion.job_stage.requires_admin_approval,
          requires_scheduling: completion.job_stage.requires_scheduling,
          requires_documents: completion.job_stage.requires_documents,
          has_forms: completion.job_stage.associated_forms?.length > 0,
          min_score_required: completion.job_stage.min_score_required,
        },
        completion_metadata: {
          has_scores: !!completion.form_scores,
          is_scheduled: !!completion.scheduled_data,
          has_documents: !!completion.documents,
          needs_admin_action: completion.requires_admin_action,
        },
      }));

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Stage progress retrieved successfully',
        data: enhancedProgress,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Get stages requiring admin action
   * @param req Request
   * @param res Response
   */
  @Get('admin-actions')
  async getStagesRequiringAdminAction(
    @Req() req: Request,
    @Res() res: Response,
    @Query('status') status?: string,
  ) {
    try {
      // This would get all stage completions that require admin action
      // Implementation would be added based on your specific requirements
      
      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Admin action items retrieved successfully',
        data: [], // Placeholder
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Manually advance stage (admin override)
   * @param req Request
   * @param res Response
   * @param stageCompletionDto StageCompletionV2Dto
   */
  @Post('manual-advance')
  @UsePipes(new ValidationPipe())
  async manualAdvanceStage(
    @Req() req: Request,
    @Res() res: Response,
    @Body() stageCompletionDto: StageCompletionV2Dto,
  ) {
    try {
      const completion = await this.jobStageV2Service.completeStageV2({
        ...stageCompletionDto,
        completed_by_type: COMPLETED_BY_TYPE.ADMIN,
      });

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Stage advanced manually',
        data: completion,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
