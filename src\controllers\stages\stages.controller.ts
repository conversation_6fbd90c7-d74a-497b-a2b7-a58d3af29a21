import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Req,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthGuard } from '@nestjs/passport';

import { StageDto, UpdateStageDto } from 'src/dto/stages.dto';
import StageService from 'src/services/stages.service';
import { OrganizationAuth } from 'src/guards/organization.guard';

@Controller('stages')
export default class StageController {
  constructor(private readonly stageService: StageService) {}

  @Post('/')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async createStage(
    @Req() request: Request,
    @Body() body: StageDto,
    @Res() response: Response,
  ) {
    try {
      const stage = await this.stageService.insertStage(
        body,
        request['organization_id'],
      );
      return response
        .status(201)
        .json({ message: 'Stage created successfully!', stage });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async getStages(@Req() request: Request, @Res() response: Response) {
    try {
      const stages = await this.stageService.getStages(
        request['organization_id'],
      );
      return response.status(200).json({
        message: 'Stages fetched successfully!',
        stages,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:stage_id')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async getStage(
    @Param('stage_id') stage_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ) {
    try {
      const stage = await this.stageService.getStage(
        stage_id,
        request['organization_id'],
      );
      return response.status(200).json({
        message: 'Stages fetched successfully!',
        stage,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch('/:stage_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async updateStage(
    @Param('stage_id') stage_id: string,
    @Body() body: UpdateStageDto,
    @Req() request: Request,
    @Res() response: Response,
  ) {
    try {
      const stage = await this.stageService.updateStage(
        body,
        stage_id,
        request['organization_id'],
      );
      return response.status(200).json({
        message: 'Stage updated successfully!',
        stage,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('/:stage_id')
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async deleteStage(
    @Param('stage_id') stage_id: string,
    @Req() request: Request,
    @Res() response: Response,
  ) {
    try {
      const isDeleted = await this.stageService.deleteStage(
        stage_id,
        request['organization_id'],
      );
      return response.status(isDeleted ? 200 : 422).json({
        message: isDeleted
          ? 'Stage deleted successfully!'
          : 'Unable to process your request. Please try again later.',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
