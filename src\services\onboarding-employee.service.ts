import {
  HttpStatus,
  HttpException,
  Injectable,
  NotFoundException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  capitalizeFirstLetter,
  generateBcryptHash,
  generateUUID,
} from 'src/util';
import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  In,
  Repository,
} from 'typeorm';
import { AppsService } from './apps.service';
import { OrganizationService } from './organization.service';

import { User } from 'src/entities/user.entity';
import { UserDto } from 'src/dto/user.dto';
import {
  EMPLOYEE_STATUS,
  OnboardingEmployee,
} from 'src/entities/onboarding-employee.entity';
import {
  OnboardingEmployeeDto,
  UpdateOnboardingEmployeeDto,
} from 'src/dto/onboarding-employee.dto';
import { FormValueService } from './formvalues.service';
import {
  FormValuesRepository,
  FormValuesRepositoryDocument,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import { Model, QueryOptions } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Organization } from 'src/entities/organization.entity';
import { Apps } from 'src/entities/apps.entity';
import { OrgFormsRepositoryService } from './orgformsrepository.service';
import { FIELD } from 'src/util/interfaces/forms.interface';
import { OrgFormsRepository } from 'src/entities/mongodb/orgformsrepository.entity';
import { Configurations } from 'src/entities/configurations.entity';
import { SendGridService } from 'src/util/sendgrid.service';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { UserService } from './user.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { OnBoardEmpChecklistService } from './onboard-emp-checklist.service';
import { JobService } from './job.service';
import { Job } from 'src/entities/job.entity';
import { JobVersionService } from './job-version.service';
import { JobVersion } from 'src/entities/job-version.entity';
import { JobStageService } from './job-stage.service';

@Injectable()
export class OnboardingEmployeeService {
  constructor(
    @InjectRepository(OnboardingEmployee, 'mysql')
    private onboardingEmployeeRepository: Repository<OnboardingEmployee>,

    @InjectModel(FormValuesRepository.name)
    private readonly FormValueRepository: Model<FormValuesRepositoryDocument>,

    @InjectRepository(User, 'mysql')
    private UserRepository: Repository<User>,

    @InjectRepository(Configurations, 'mysql')
    private configurationsRepository: Repository<Configurations>,

    @InjectRepository(IndustryAppProcess, 'mysql')
    private industryAppProcessRepository: Repository<IndustryAppProcess>,

    private readonly appService: AppsService,

    @Inject(forwardRef(() => OrganizationService))
    private readonly organizationService: OrganizationService,

    private readonly formValueService: FormValueService,

    @Inject(forwardRef(() => OrgFormsRepositoryService))
    private readonly orgFormsRepositoryService: OrgFormsRepositoryService,

    private readonly sendGridService: SendGridService,

    @Inject(forwardRef(() => OnBoardEmpChecklistService))
    private readonly OnBoardEmpChecklistService: OnBoardEmpChecklistService,

    // private readonly OnboardingEmployeeService: OnboardingEmployeeService,

    private readonly OnBoardOrgChecklistService: OnBoardOrgChecklistService,

    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,

    @Inject(forwardRef(() => JobService))
    private readonly jobService: JobService,

    @Inject(forwardRef(() => JobVersionService))
    private readonly jobVersionService: JobVersionService,

    @Inject(forwardRef(() => JobStageService))
    private readonly jobStageService: JobStageService,
  ) {}

  public select: FindOptionsSelect<OnboardingEmployee> = {
    id: false,
    onboarding_employee_id: true,
    name: true,
    email: true,
    mobile_number: true,
    bg_verification_document: true,
    status: true,
    scheduled_interviews: true,
    is_rejected: true,
    password: false,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Create OnboardingEmployee
   * @param onboarding_employeeDto OnboardingEmployeeDto
   * @returns Promise<OnboardingEmployee>
   */
  async create(
    onboarding_employeeDto: OnboardingEmployeeDto,
    organization_id: any,
  ): Promise<OnboardingEmployee> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id,
        },
        false,
        { id: true, name: true, email: true, pass_key: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const {
        id,
        name: organizationName,
        email: organizationEmail,
        pass_key,
      } = organization;

      if (
        (await this.userService.findOne(
          {
            email: onboarding_employeeDto.email,
            organization: { id },
          },
          false,
          { ...this.userService.select, id: true },
        )) ||
        (await this.userService.findOne(
          {
            mobile_number: onboarding_employeeDto.mobile_number,
            organization: { id },
          },
          false,
          { ...this.userService.select, id: true },
        ))
      ) {
        throw new HttpException(
          'Applicant already exists',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (
        (await this.findOne(
          {
            email: onboarding_employeeDto.email,
            organization: { id },
          },
          false,
          { ...this.select, id: true },
        )) ||
        (await this.findOne(
          {
            mobile_number: onboarding_employeeDto.mobile_number,
            organization: { id },
          },
          false,
          { ...this.select, id: true },
        ))
      ) {
        throw new HttpException(
          'Applicant already exists',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Handle job relationship if job_id is provided
      let job: Job = null;
      let jobVersion: JobVersion = null;
      if (onboarding_employeeDto.job_id) {
        job = await this.jobService.findOne(
          { job_id: onboarding_employeeDto.job_id, organization: { id } },
          false,
          { id: true, title: true },
        );

        if (!job) {
          throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
        }

        // Get current job version for the employee
        jobVersion = await this.jobVersionService.getCurrentVersion(
          onboarding_employeeDto.job_id,
        );
      }

      const onboarding_employee = await this.onboardingEmployeeRepository.save({
        ...onboarding_employeeDto,
        password: await generateBcryptHash(onboarding_employeeDto.password),
        onboarding_employee_id: generateUUID(),
        // app,
        organization: { id },
        job: job ? { id: job.id } : null,
        job_version: jobVersion ? { id: jobVersion.id } : null,
        application_start_date:
          onboarding_employeeDto.application_start_date || null,
        application_end_date:
          onboarding_employeeDto.application_end_date || null,
      });

      if (onboarding_employee) {
        // Initialize stage completions if job has stages
        if (job) {
          try {
            await this.jobStageService.initializeStageCompletions(
              onboarding_employee,
            );
          } catch (error) {
            // Log error but don't fail the employee creation
            console.error('Failed to initialize stage completions:', error);
          }
        }

        const onboarding_app_process =
          await this.industryAppProcessRepository.findOne({
            where: {
              process_code: 'HC_OBRD',
            },
          });

        const onboarding_app = await this.appService.findOne({
          organizations: { id },
          industry_app_process: { id: onboarding_app_process.id },
        });

        const emailConfigDetails: any =
          await this.configurationsRepository.findOne({
            where: {
              organization: { id },
              type: 'email',
            },
          });
        //TODO:Add Application link
        if (emailConfigDetails && emailConfigDetails.name == 'send-grid') {
          const mail = {
            to: {
              email: onboarding_employee.email,
              name: capitalizeFirstLetter(organizationName),
            },
            subject: `Welcome to ${capitalizeFirstLetter(
              organizationName,
            )}! Download Your Onboarding App`,
            from: emailConfigDetails.details.fromEmail,
            text: ' ',
            html: `<html>
                    <head></head>
                    <body>
                        <p>Hi ${capitalizeFirstLetter(
                          onboarding_employee?.name,
                        )}, We are happy to inform you that you have been shortlisted for the Caregiver at ${capitalizeFirstLetter(
              organizationName,
            )}! As part of our hiring process, you need to update your profile in our hiring portal. Below are the credentials to access our hiring portal. </p>
                        <br/>
                        <p><b>Your Account Details: </b></p>
                        <ul style="list-style-type: none;">
                          <li><b>Username:</b> ${onboarding_employee.email}</li>
                          <li><b>Password:</b> ${
                            onboarding_employeeDto.password
                          }</li>
                           <li><b>Passkey:</b> ${pass_key}</li>
                        </ul>
                        <p style="font-size: 12px; color: gray;">Tip: Select the text and press Ctrl+C (Cmd+C on Mac) to copy.</p>
                        <br/>
                        <p><b>Steps to follow: </b></p>
                        <ol style="list-style-type: number;">
                        <li>
                          Download the "Caregiver Onboarding" application:
                          <ul>
                            <li>
                              <b>Android (Play Store):</b>
                              <a href="https://play.google.com/store/apps/details?id=com.klezafab.caregiver">
                                Download on Play Store
                              </a>
                            </li>
                            <li>
                              <b>iOS (App Store):</b>
                              <a href="https://apps.apple.com/in/app/caregiver-onboarding/id6744400736">
                                Download on App Store
                              </a>
                            </li>
                          </ul>
                        </li>  
                        <li>Log-in to the account using above credentials</li>
                          <li>Update your profile  </li>
                          <li>Complete all necessary sections </li>
                        </ol>
                        <p>If you have any questions or encounter any issues, please feel free to reach out to us at <a href="mailto:${organizationEmail}">${organizationEmail}</a>. </p>
                        <br/>
                        <p>Thank you for considering a career with ${capitalizeFirstLetter(
                          organizationName,
                        )}. </p>
                      </body>
                  </html>`,
          };

          await this.sendGridService.send(
            mail,
            emailConfigDetails.details.apiKey,
          );
        }

        return onboarding_employee;
      } else {
        throw new HttpException(
          'Onboarding Employee not created',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update OnboardingEmployee
   * @param onboarding_employeeDto OnboardingEmployeeDto
   * @returns Promise<OnboardingEmployee>
   */

  async update(
    onboarding_employee_id: string,
    onboarding_employeeDto: UpdateOnboardingEmployeeDto,
  ): Promise<OnboardingEmployee> {
    try {
      const data: OnboardingEmployee = await this.findOne(
        { onboarding_employee_id },
        false,
        {
          ...this.select,
          password: true,
          id: true,
        },
        { organization: true },
      );

      if (!data)
        throw new HttpException(
          'OnboardingEmployee Not Found',
          HttpStatus.NOT_FOUND,
        );

      let updateOnboardingEmployee: any = {
        ...onboarding_employeeDto,
        id: data.id,
        scheduled_interviews: data.scheduled_interviews
          ? data.scheduled_interviews
          : [],
        is_rejected: false,
      };

      if (onboarding_employeeDto?.password) {
        updateOnboardingEmployee.password = await generateBcryptHash(
          onboarding_employeeDto.password,
        );
      }

      // Handle job relationship if job_id is provided
      if (onboarding_employeeDto.job_id) {
        const job = await this.jobService.findOne(
          {
            job_id: onboarding_employeeDto.job_id,
            organization: data.organization,
          },
          false,
          { id: true, title: true },
        );

        if (!job) {
          throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
        }

        // Get current job version for the employee
        const jobVersion = await this.jobVersionService.getCurrentVersion(
          onboarding_employeeDto.job_id,
        );

        updateOnboardingEmployee.job = { id: job.id };
        updateOnboardingEmployee.job_version = jobVersion
          ? { id: jobVersion.id }
          : null;
      }

      // Handle application dates
      if (onboarding_employeeDto.application_start_date) {
        updateOnboardingEmployee.application_start_date =
          onboarding_employeeDto.application_start_date;
      }
      if (onboarding_employeeDto.application_end_date) {
        updateOnboardingEmployee.application_end_date =
          onboarding_employeeDto.application_end_date;
      }

      switch (updateOnboardingEmployee?.status) {
        case EMPLOYEE_STATUS.REJECTED:
          try {
            const mail = {
              to: data.email,
              subject: 'Regretting to inform you',
              from: '',
              text: 'Hai',
              html: `
                    <html>
                      <head></head>
                      <body>
                          <h5>Dear ${data.name.toUpperCase()}</h5>
                          <br/>
                          <p>We regret to inform you that we will not be moving forward with your application. Thank you for your interest and effort. </p>
                          <br/>
                          <p>We wish you all the best in your future endeavors. </p>
                          <br/>
                          <p>Best regards, </p>
                          <p>HR Manager, </p>
                          <p>${
                            data?.organization
                              ? data?.organization?.name?.toUpperCase()
                              : 'The Kleza FAB Team'
                          } </p>
                      </body>
                    </html>
              `,
            };

            delete updateOnboardingEmployee.status;

            updateOnboardingEmployee.is_rejected = true;

            const organization_id: any = data.organization.organization_id;

            await this.sendGridService.sendWithOrganizationId(
              organization_id,
              mail,
            );
          } catch (error) {
            throw new HttpException(
              error?.message || 'Something went wrong. Please try again later.',
              error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
          break;
        case EMPLOYEE_STATUS.EMPLOYEE_ONBOARD_COMPLETED:
          try {
            let apps: any;
            if (
              updateOnboardingEmployee?.appIds &&
              updateOnboardingEmployee.appIds.length
            ) {
              apps = await this.appService.list(
                { app_id: In(updateOnboardingEmployee.appIds) },
                { id: true },
              );

              if (apps.length !== updateOnboardingEmployee.appIds.length) {
                throw {
                  status: HttpStatus.BAD_REQUEST,
                  message: {
                    statusCode: HttpStatus.BAD_REQUEST,
                    message: ['Please select the existed App only.'],
                    error: 'Bad Request',
                  },
                };
              }
            }

            if (data) {
              const onboarding_app_process =
                await this.industryAppProcessRepository.findOne({
                  where: {
                    process_code: 'HC_OBRD',
                  },
                });

              const onboarding_app = await this.appService.findOne({
                organizations: { id: data.organization.id },
                industry_app_process: { id: onboarding_app_process.id },
              });

              const emailConfigDetails: any =
                await this.configurationsRepository.findOne({
                  where: {
                    organization: { id: data.organization.id },
                    type: 'email',
                  },
                });

              if (
                emailConfigDetails &&
                emailConfigDetails.name == 'send-grid'
              ) {
                const mail = {
                  to: data.email,
                  subject: 'Employee onboarded successfully',
                  from: emailConfigDetails.details.fromEmail,
                  text: ' ',
                  html: `<html>
                            <head></head>
                            <body>
                                <p>Dear ${data.name.toUpperCase()},</p>
                                <br/>
                                <p>Congratulations! You have been selected for the Caregiver position at ${data.organization.name?.toUpperCase()}. </p>
                                <br/>
                                <p>Your onboarding process is completed successfully. </p>
                                <br/>
                                <p>If you encounter any issues or have questions, please reach out to your organization's Admin or contact our support team at <a href="mailto:${
                                  data?.organization?.email
                                }">${data?.organization?.email}</a> </p>
                                <br/>
                                <p>Welcome to the team!</p>
                                <br/>
                                <p>Best regards,</p>
                                <p>HR Manager, </p>
                                <p>${
                                  data?.organization
                                    ? data?.organization?.name?.toUpperCase()
                                    : 'The Kleza FAB Team'
                                } </p>
                                </body>
                          </html>`,
                };

                await this.sendGridService.send(
                  mail,
                  emailConfigDetails.details.apiKey,
                );
              }
            }

            await this.UserRepository.save({
              name: data.name,
              email: data.email,
              mobile_number: data.mobile_number,
              organization: data.organization,
              apps,
              onboarding_employee: data,
              password: data.password,
              user_id: generateUUID(),
            });
          } catch (error) {
            throw new HttpException(
              error?.message || 'Something went wrong. Please try again later.',
              error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
          break;
        case EMPLOYEE_STATUS.INTERVIEW_SCHEDULED ||
          EMPLOYEE_STATUS.INTERVIEW_RE_SCHEDULED:
          updateOnboardingEmployee.scheduled_interviews.push({
            scheduled_interview_time:
              updateOnboardingEmployee?.scheduled_interview_time,
            scheduled_interview_date:
              updateOnboardingEmployee?.scheduled_interview_date,
          });
          break;
        case EMPLOYEE_STATUS.INTERVIEW_COMPLETED:
          try {
            const mail = {
              to: data.email,
              subject: 'Interview completed successfully',
              from: '',
              text: 'Hai',
              html: `
                      <html>
                            <head></head>
                            <body>
                                <p>Dear ${data.name.toUpperCase()},</p>
                                <br/>
                                <p>Congratulations! You have been selected for the next stage. </p>
                                <br/>
                                <p>Please login to your account and complete the Orientation, read Policies and Guidelines. </p>
                                <br/>
                                <p>If you encounter any issues or have questions, please reach out to your organization's Admin or contact our support team at <a href="mailto:${
                                  data?.organization?.email
                                }">${data?.organization?.email}</a> </p>
                                <br/>
                                <p>Welcome to the team!</p>
                                <br/>
                                <p>Best regards,</p>
                                <p>HR Manager, </p>
                                <p>${
                                  data?.organization
                                    ? data?.organization?.name?.toUpperCase()
                                    : 'The Kleza FAB Team'
                                } </p>
                                </body>
                      </html>`,
            };

            const organization_id: any = data.organization.organization_id;

            await this.sendGridService.sendWithOrganizationId(
              organization_id,
              mail,
            );
          } catch (error) {
            throw new HttpException(
              error?.message || 'Something went wrong. Please try again later.',
              error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
          break;
        case EMPLOYEE_STATUS.IN_PERSON_ORIENTATION_COMPLETED:
          try {
            const mail = {
              to: data.email,
              subject: 'In Person orientation completed successfully',
              from: '',
              text: 'Hai',
              html: `
                  <html>
                        <head></head>
                        <body>
                            <p>Dear ${data.name.toUpperCase()},</p>
                            <br/>
                            <p>Congratulations! You have completed In Person Orientation process successfully. </p>
                            <br/>
                            <p>If you encounter any issues or have questions, please reach out to your organization's Admin or contact our support team at <a href="mailto:${
                              data?.organization?.email
                            }">${data?.organization?.email}</a> </p>
                            <br/>
                            <p>Welcome to the team!</p>
                            <br/>
                            <p>Best regards,</p>
                            <p>HR Manager, </p>
                                <p>${
                                  data?.organization
                                    ? data?.organization?.name?.toUpperCase()
                                    : 'The Kleza FAB Team'
                                } </p>
                            </body>
                  </html>`,
            };

            const organization_id: any = data.organization.organization_id;

            await this.sendGridService.sendWithOrganizationId(
              organization_id,
              mail,
            );
          } catch (error) {
            throw new HttpException(
              error?.message || 'Something went wrong. Please try again later.',
              error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
        default:
          break;
      }

      const updated_data = await this.onboardingEmployeeRepository.save(
        updateOnboardingEmployee,
      );

      return updated_data;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find OnboardingEmployee
   * @param findBy Object "{column: value}"
   * @returns Promise<OnboardingEmployee>
   */
  async findOne(
    findBy: FindOptionsWhere<OnboardingEmployee>,
    withDeleted = false,
    select = this.select,
    relations: FindOptionsRelations<OnboardingEmployee> = {},
  ): Promise<OnboardingEmployee> {
    try {
      return await this.onboardingEmployeeRepository.findOne({
        where: findBy,
        withDeleted,
        select,
        relations,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find OnboardingEmployee
   * @param findBy Object "{column: value}"
   * @returns Promise<OnboardingEmployee>
   */
  async findOneWithFullDetails(
    findBy: FindOptionsWhere<OnboardingEmployee>,
    withDeleted = false,
    select = this.select,
    relations: FindOptionsRelations<OnboardingEmployee> = {},
    organization_id: string,
  ): Promise<any> {
    try {
      let onboardingEmployee: any =
        await this.onboardingEmployeeRepository.findOne({
          where: findBy,
          withDeleted,
          select,
          relations,
        });

      if (!onboardingEmployee) {
        throw new NotFoundException('Onboarding Employee not found');
      }

      const onBoardOrgChecklistDetails =
        await this.OnBoardEmpChecklistService.findOne({
          onboarding_employee_id: onboardingEmployee.onboarding_employee_id,
        });

      onboardingEmployee.documents =
        await this.formValueService.getDocumentsInfo(
          organization_id,
          onboardingEmployee.onboarding_employee_id,
        );

      if (!onBoardOrgChecklistDetails) {
        const onBoardOrgChecklistDetails =
          await this.OnBoardOrgChecklistService.findOne({
            organization_id,
          });

        return {
          ...onboardingEmployee,
          onboard_org_checklist: onBoardOrgChecklistDetails,
        };
      } else {
        const { completed_checklist, emp_checklist_id, onboard_org_checklist } =
          onBoardOrgChecklistDetails;

        return {
          ...onboardingEmployee,
          completed_checklist,
          emp_checklist_id,
          onboard_org_checklist,
        };
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find OnboardingEmployees
   * @param findBy Object "{column: value}"
   * @returns Promise<OnboardingEmployee[]>
   */
  async find(
    findBy: FindOptionsWhere<OnboardingEmployee>,
    withDeleted = false,
    select = this.select,
    relations: FindOptionsRelations<OnboardingEmployee> = {},
  ): Promise<OnboardingEmployee[]> {
    try {
      const onboardingEmployees: any =
        await this.onboardingEmployeeRepository.find({
          where: findBy,
          withDeleted,
          relations,
          select,
        });

      return await this.employeesWithFormStatus(
        onboardingEmployees,
        findBy.organization,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async employeesWithFormStatus(
    onboardingEmployees: OnboardingEmployee[],
    organizations: any,
  ) {
    try {
      let getApps: any = await this.appService.list(
        {
          organizations,
        },
        null,
        null,
        { industry_app_process: true },
      );

      const onboardingApps = ['HC_OBRD'];

      getApps = getApps.filter((app: Apps) =>
        onboardingApps.includes(app?.industry_app_process?.process_code),
      );

      const app: Apps = getApps ? getApps[0] : null;

      const organization = await this.organizationService.findOne(
        {
          id: organizations.id,
        },
        false,
      );

      // Set the basic query for organization forms
      const findBy: QueryOptions<OrgFormsRepository> = {
        app_id: app?.app_id,
        organization: organization?.organization_id,
        has_sub_forms: false,
        status: true,
      };

      // Add the logic for checking orientation forms
      const orientationFormsQuery: QueryOptions<OrgFormsRepository> = {
        ...findBy,
        is_quiz_form: true, // This flag identifies orientation forms
      };

      // Non-orientation forms logic
      findBy.$or = [
        { is_quiz_form: { $ne: true } },
        { is_quiz_form: { $exists: false } },
      ];

      // Fetch both types of forms
      const organizationForms = await this.orgFormsRepositoryService.find(
        findBy,
        false,
        {
          ...this.orgFormsRepositoryService.select,
          _id: true,
        },
      );

      const organizationFormsIds = organizationForms.map(
        (organizationForm: any) => organizationForm._id,
      );

      // Fetch orientation forms separately
      const orientationForms = await this.orgFormsRepositoryService.find(
        orientationFormsQuery,
        false,
        {
          ...this.orgFormsRepositoryService.select,
          _id: true,
        },
      );

      const orientationFormsIds = orientationForms.map(
        (orientationForm: any) => orientationForm._id,
      );

      await Promise.all(
        onboardingEmployees.map(async (onboardingEmployee: any) => {
          // Non-orientation form status
          const employeeFormValues = await this.formValueService.find({
            user_id: onboardingEmployee.onboarding_employee_id,
            form: { $in: organizationFormsIds },
          });

          let formFillingStatus = 'Application Completed';

          if (!employeeFormValues.length) {
            formFillingStatus = 'Not Started';
          } else if (organizationForms.length === employeeFormValues.length) {
            employeeFormValues.map((formValues) => {
              const getCompleted = () => {
                if (formValues) {
                  if (formValues.completed) {
                    return formValues.completed;
                  }
                  const values = formValues.values;
                  const keys = Object.keys(values);
                  if (keys.length) {
                    keys.map((key) => {
                      const requiredFieldsKey = formValues.form.fields[
                        key
                      ].fields.filter(
                        (field) => field.validation_schema.required,
                      );
                      requiredFieldsKey.forEach((field: any) => {
                        return field.is_iterative_or_not
                          ? values[key][field.name].length > 0
                            ? true
                            : false
                          : values[key][field.name]
                          ? true
                          : false;
                      });
                    });
                  }
                }
                return false;
              };
              if (getCompleted() == false) {
                formFillingStatus = 'In Progress';
              }
            });
          } else {
            formFillingStatus = 'In Progress';
          }

          onboardingEmployee.form_filling_status = formFillingStatus;

          // Orientation form status
          const orientationFormValues = await this.formValueService.find({
            user_id: onboardingEmployee.onboarding_employee_id,
            form: { $in: orientationFormsIds },
          });

          let orientationStatus = 'Orientation Completed';
          let OrientationResult = [];
          let OrientationResultString = '';

          if (!orientationFormValues.length) {
            orientationStatus = 'Orientation Not Started';
          } else if (orientationForms.length === orientationFormValues.length) {
            orientationFormValues.map((formValues) => {
              if (!formValues.completed) {
                orientationStatus = 'Orientation In Progress';
              } else {
                if (
                  formValues?.total_points &&
                  formValues?.scored_points != 0
                ) {
                  OrientationResult.push({
                    form_name: formValues?.form?.name,
                    total_points: formValues?.total_points,
                    scored_points: formValues?.scored_points,
                  });
                }
              }
            });
          } else {
            orientationStatus = 'Orientation In Progress';
          }

          if (OrientationResult && OrientationResult.length > 0) {
            OrientationResultString = `
            <p>Below are your quiz results:</p>
            <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
              <thead>
                <tr>
                  <th>Quiz Name</th>
                  <th>Result</th>
                </tr>
              </thead>
              <tbody>
                ${OrientationResult.map(
                  (quiz) => `
                <tr>
                  <td align="center">${quiz.form_name}</td>
                  <td align="center">${quiz.scored_points}/${quiz.total_points}</td>
                </tr>
                `,
                ).join('')}
              </tbody>
            </table>
            <br />
            `;
          }

          onboardingEmployee.orientationResultString = OrientationResultString;

          onboardingEmployee.orientation_status = orientationStatus;

          if (onboardingEmployee.status == EMPLOYEE_STATUS.IN_PROGRESS) {
            onboardingEmployee.status = onboardingEmployee.form_filling_status;
          } else if (
            onboardingEmployee.status == EMPLOYEE_STATUS.INTERVIEW_COMPLETED
          ) {
            onboardingEmployee.status = onboardingEmployee.orientation_status;
          }

          return onboardingEmployee;
        }),
      );

      return onboardingEmployees;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete
   * @param onboarding_employee_id string ID of the user
   * @param organization_id string
   * @returns Promise<Boolean>
   */
  async delete(
    onboarding_employee_id: string,
    organization_id?: string,
  ): Promise<Boolean> {
    try {
      const findBy: FindOptionsWhere<OnboardingEmployee> = {
        onboarding_employee_id,
      };

      if (organization_id) {
        const organization = await this.organizationService.findOne(
          {
            organization_id,
          },
          false,
          { id: true },
        );
        findBy.organization = organization;
      }

      const user = await this.findOne(findBy, false, {
        ...this.select,
        id: true,
      });

      if (!user) throw new NotFoundException('Applicant not found');
      return this.onboardingEmployeeRepository.softDelete(findBy)
        ? true
        : false;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
