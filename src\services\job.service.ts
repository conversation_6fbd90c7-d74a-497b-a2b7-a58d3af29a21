import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  FindOptionsSelect,
  FindOptionsWhere,
  FindOptionsRelations,
  Repository,
} from 'typeorm';
import { Job } from 'src/entities/job.entity';
import { CreateJobDto, UpdateJobDto } from 'src/dto/job.dto';
import { generateUUID } from 'src/util';
import { Organization } from 'src/entities/organization.entity';
import { JobVersionService } from './job-version.service';

@Injectable()
export class JobService {
  constructor(
    @InjectRepository(Job, 'mysql')
    private jobRepository: Repository<Job>,
    private jobVersionService: JobVersionService,
  ) {}

  public select: FindOptionsSelect<Job> = {
    id: false,
    job_id: true,
    title: true,
    department: true,
    description: true,
    requirements: true,
    salary_range: true,
    employment_type: true,
    location: true,
    status: true,
    version: true,
    application_start_date: true,
    application_end_date: true,
    created_at: false,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Create Job
   * @param CreateJobDto CreateJobDto
   * @param organization Organization
   * @returns Promise<Job>
   */
  async create(
    createJobDto: CreateJobDto,
    organization: Organization,
  ): Promise<Job> {
    try {
      const job = await this.jobRepository.create({
        ...createJobDto,
        job_id: generateUUID(),
        organization,
        version: 1,
      });

      const savedJob = await this.jobRepository.save(job);

      // Create initial version
      await this.jobVersionService.createVersion(
        savedJob,
        'Initial job creation',
      );

      return savedJob;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find One Job
   * @param findBy Object "{column: value}"
   * @param withDeleted boolean
   * @param select FindOptionsSelect<Job>
   * @returns Promise<Job>
   */
  async findOne(
    findBy: FindOptionsWhere<Job>,
    withDeleted = false,
    select = this.select,
    relations: FindOptionsRelations<Job> = {},
  ): Promise<Job> {
    try {
      return await this.jobRepository.findOne({
        where: findBy,
        withDeleted,
        select,
        relations: {
          organization: true,
          ...relations,
        },
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find Jobs
   * @param findBy Object "{column: value}"
   * @param withDeleted boolean
   * @param select FindOptionsSelect<Job>
   * @returns Promise<Job[]>
   */
  async find(
    findBy: FindOptionsWhere<Job>,
    withDeleted = false,
    select = this.select,
  ): Promise<Job[]> {
    try {
      return await this.jobRepository.find({
        where: findBy,
        withDeleted,
        select,
        relations: {
          organization: true,
        },
        order: {
          created_at: 'DESC',
        },
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update Job
   * @param job_id string
   * @param updateJobDto UpdateJobDto
   * @returns Promise<Job>
   */
  async update(job_id: string, updateJobDto: UpdateJobDto): Promise<Job> {
    try {
      const job = await this.findOne(
        { job_id },
        false,
        { ...this.select, id: true },
        { organization: true },
      );

      if (!job) {
        throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
      }

      // Increment version for significant changes
      const newVersion = job.version + 1;
      const updatedJobData = {
        ...updateJobDto,
        version: newVersion,
      };

      await this.jobRepository.update({ job_id }, updatedJobData);

      const updatedJob = await this.findOne(
        { job_id },
        false,
        { ...this.select, id: true },
        { organization: true },
      );

      // Create new version with change reason
      await this.jobVersionService.createVersion(
        updatedJob,
        updateJobDto.change_reason || 'Job updated',
      );

      return updatedJob;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete Job
   * @param job_id string
   * @returns Promise<boolean>
   */
  async delete(job_id: string): Promise<boolean> {
    try {
      const job = await this.findOne({ job_id }, false, { id: true });

      if (!job) {
        throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
      }

      await this.jobRepository.softDelete({ job_id });
      return true;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Job History
   * @param job_id string
   * @returns Promise<JobVersion[]>
   */
  async getJobHistory(job_id: string) {
    try {
      return await this.jobVersionService.getJobHistory(job_id);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Current Job Version
   * @param job_id string
   * @returns Promise<JobVersion>
   */
  async getCurrentJobVersion(job_id: string) {
    try {
      return await this.jobVersionService.getCurrentVersion(job_id);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
