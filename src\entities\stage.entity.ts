import {
  BeforeInsert,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { generateUUID } from 'src/util';
import { StageActionBy } from 'src/util/enums';
import { Organization } from './organization.entity';

@Entity()
export class Stages {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', unique: true, name: 'stage_id' })
  stage_id: string;

  @Column({ type: 'varchar' })
  title: string;

  @Column({ type: 'longtext', nullable: true })
  description: string;

  @Column({ type: 'json', nullable: true })
  forms_list: string[];

  @Column({
    type: 'enum',
    enum: Object.values(StageActionBy),
  })
  action_by: StageActionBy;

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;

  @BeforeInsert()
  generateStageId() {
    this.stage_id = generateUUID();
  }
}
