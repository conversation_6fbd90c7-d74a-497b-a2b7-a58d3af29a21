import {
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  Repository,
} from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { filteredObj } from 'src/util';
import { AppsService } from './apps.service';
import { Stages } from 'src/entities/stage.entity';
import { OrganizationService } from './organization.service';
import { StageDto, UpdateStageDto } from 'src/dto/stages.dto';
import { OrgFormsRepositoryService } from './orgformsrepository.service';
import { StageByIdInterface } from 'src/util/interfaces/stages.interface';

export default class StageService {
  constructor(
    @InjectRepository(Stages, 'mysql')
    private readonly stagesRepository: Repository<Stages>,
    private readonly appsService: AppsService,
    private readonly organizationService: OrganizationService,
    private readonly orgFormsServie: OrgFormsRepositoryService,
  ) {}

  /**
   * @variable select FindOptionsSelect<Stages>
   */
  public select: FindOptionsSelect<Stages> = {
    title: true,
    stage_id: true,
    action_by: true,
    description: true,
    forms_list: true,
  };

  /**
   * This method will save the stage record with the provided data.
   * @param data Stages
   * @returns stage
   */
  async save(data: Stages) {
    try {
      const returnData = await this.stagesRepository.save(data);
      delete returnData.id; // Remove id from the response.
      return returnData;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get List of organizations
   * @returns Promise<Organization[]>
   */
  async list(
    where: FindOptionsWhere<Stages> = null,
    select: FindOptionsSelect<Stages> = this.select,
    withDeleted: boolean = false,
    relations: FindOptionsRelations<Stages> = {},
  ): Promise<Stages[]> {
    try {
      return await this.stagesRepository.find({
        where: where,
        withDeleted,
        select,
        relations,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get Organization details by condition
   * @param findBy Object "{column: value}"
   * @returns Organization
   */
  async findOne(
    findBy: any,
    withDeleted = false,
    select: FindOptionsSelect<Stages> = this.select,
    relations: FindOptionsRelations<Stages> = {},
  ): Promise<Stages> {
    try {
      const data = await this.stagesRepository.findOne({
        where: findBy,
        withDeleted,
        select,
        relations,
      });
      if (!data) {
        throw new NotFoundException(`Stage not found`);
      }
      return data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * insert the stage into the database
   * @param data StageDto
   * @returns Promise<Stages>
   */
  async insertStage(data: StageDto, organization_id: string): Promise<Stages> {
    try {
      const stageData = {
        ...data,
        organization: await this.organizationService.findOne(
          { organization_id },
          false,
          { id: true },
        ),
        app: await this.appsService.findOne({ app_id: data.app_id }, false, {
          id: true,
        }),
      };
      const stage = this.stagesRepository.create(stageData);
      return await this.save(stage);
    } catch (error) {
      throw error;
    }
  }

  /**
   * get all stages of the organization
   * @param organization_id string
   * @returns Promise<Stages[]>
   */
  async getStages(organization_id: string): Promise<Stages[]> {
    try {
      return await this.list({
        organization: await this.organizationService.findOne(
          { organization_id },
          false,
          { id: true },
        ),
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * get stage by stage_id and organization_id
   * @param stage_id string
   * @param organization_id string
   * @returns Promise<StageByIdInterface>
   */
  async getStage(
    stage_id: string,
    organization_id: string,
  ): Promise<StageByIdInterface> {
    try {
      const data = await this.findOne(
        {
          stage_id,
          organization: await this.organizationService.findOne(
            { organization_id },
            false,
            { id: true },
          ),
        },
        false,
        { id: true, ...this.select },
      );
      delete data.id;
      return {
        ...data,
        forms_list: (
          await this.orgFormsServie.find({
            form_id: { $in: data.forms_list },
          })
        ).map((form) => ({ name: form.name, form_id: form.form_id })),
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * @description Update stage by stage_id and organization_id
   * @description In stage update apps was not updated due to technical issues.
   * @param data UpdateStageDto
   * @param stage_id string
   * @param organization_id string
   * @throws NotFoundException if stage not found
   * @returns Promise<Stages>
   */
  async updateStage(
    data: UpdateStageDto,
    stage_id: string,
    organization_id: string,
  ): Promise<Stages> {
    try {
      const stage = await this.findOne(
        {
          stage_id: stage_id,
          organization: await this.organizationService.findOne(
            {
              organization_id,
            },
            false,
            { id: true },
          ),
        },
        false,
        { id: true, ...this.select },
      );
      const stageData = this.stagesRepository.create({
        id: stage.id,
        ...filteredObj(data),
      });
      return await this.save(stageData);
    } catch (error) {
      throw error;
    }
  }

  async deleteStage(
    stage_id: string,
    organization_id: string,
  ): Promise<boolean> {
    try {
      await this.findOne(
        {
          stage_id,
          organization: await this.organizationService.findOne(
            {
              organization_id,
            },
            false,
            { id: true },
          ),
        },
        false,
        { id: true },
      );
      return (await this.stagesRepository.softDelete({ stage_id }))
        ? true
        : false;
    } catch (error) {
      throw error;
    }
  }
}
