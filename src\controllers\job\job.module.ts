import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import JobController from './job.controller';
import { JobService } from 'src/services/job.service';
import { Job } from 'src/entities/job.entity';
import { JobVersion } from 'src/entities/job-version.entity';
import { JobStage } from 'src/entities/job-stage.entity';
import { JobStageCompletion } from 'src/entities/job-stage-completion.entity';
import { Stages } from 'src/entities/stage.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { Organization } from 'src/entities/organization.entity';
import { OrganizationService } from 'src/services/organization.service';
import { JobVersionService } from 'src/services/job-version.service';
import { JobStageService } from 'src/services/job-stage.service';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { Configurations } from 'src/entities/configurations.entity';
import { User } from 'src/entities/user.entity';
import { OrganizationModule } from '../organization/organization.module';
import { AppsService } from 'src/services/apps.service';
import { Apps } from 'src/entities/apps.entity';
import { IndustryAppProcessService } from 'src/services/industry-app-process.service';
import { PaginationService } from 'src/util/pagination.service';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';

@Module({
  imports: [
    forwardRef(() => OrganizationModule),
    TypeOrmModule.forFeature(
      [
        Job,
        JobVersion,
        JobStage,
        JobStageCompletion,
        Stages,
        OnboardingEmployee,
        Organization,
        OrgAppConfiguration,
        Configurations,
        User,
        Apps,
        IndustryAppProcess,
      ],
      'mysql',
    ),
  ],
  exports: [JobService, JobVersionService, JobStageService],
  providers: [
    JobService,
    JobVersionService,
    JobStageService,
    OrganizationService,
    AppsService,
    IndustryAppProcessService,
    PaginationService,
  ],
  controllers: [JobController],
})
export class JobModule {}
