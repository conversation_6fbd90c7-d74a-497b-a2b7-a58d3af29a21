import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import <PERSON><PERSON>ontroller from './job.controller';
import { JobService } from 'src/services/job.service';
import { Job } from 'src/entities/job.entity';
import { JobVersion } from 'src/entities/job-version.entity';
import { Organization } from 'src/entities/organization.entity';
import { OrganizationService } from 'src/services/organization.service';
import { JobVersionService } from 'src/services/job-version.service';

@Module({
  imports: [TypeOrmModule.forFeature([Job, JobVersion, Organization], 'mysql')],
  exports: [JobService, JobVersionService],
  providers: [JobService, JobVersionService, OrganizationService],
  controllers: [JobController],
})
export class JobModule {}
