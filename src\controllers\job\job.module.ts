import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import JobController from './job.controller';
import { JobService } from 'src/services/job.service';
import { Job } from 'src/entities/job.entity';
import { JobVersion } from 'src/entities/job-version.entity';

import { JobStageCompletion } from 'src/entities/job-stage-completion.entity';
import { Stages } from 'src/entities/stage.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { Organization } from 'src/entities/organization.entity';
import { OrganizationService } from 'src/services/organization.service';
import { JobVersionService } from 'src/services/job-version.service';
import { JobStageService } from 'src/services/job-stage.service';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        Job,
        JobVersion,

        JobStageCompletion,
        Stages,
        OnboardingEmployee,
        Organization,
      ],
      'mysql',
    ),
  ],
  exports: [JobService, JobVersionService, JobStageService],
  providers: [
    JobService,
    JobVersionService,
    JobStageService,
    OrganizationService,
  ],
  controllers: [JobController],
})
export class JobModule {}
