import {
  Injectable,
  HttpException,
  HttpStatus,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobStage } from 'src/entities/job-stage.entity';
import {
  JobStageCompletion,
  STAGE_COMPLETION_STATUS,
  COMPLETED_BY_TYPE,
  STAGE_TYPE,
  FORM_TYPE,
} from 'src/entities/job-stage-completion.entity';
import {
  OnboardingEmployee,
  EMPLOYEE_STATUS,
} from 'src/entities/onboarding-employee.entity';
import { Organization } from 'src/entities/organization.entity';
import { User } from 'src/entities/user.entity';
import { OnboardingEmployeeService } from './onboarding-employee.service';
import { OrgFormsRepositoryService } from './orgformsrepository.service';
import { FormValueService } from './form-value.service';
import { SendGridService } from './sendgrid.service';

export interface StageCompletionV2Dto {
  job_stage_id: string;
  onboarding_employee_id: string;
  status: STAGE_COMPLETION_STATUS;
  completed_by_type: COMPLETED_BY_TYPE;
  completion_notes?: string;
  completion_data?: any;
  // V2 Enhanced fields
  form_scores?: any;
  scheduled_data?: any;
  documents?: any;
  admin_notes?: string;
  rejection_reason?: string;
  auto_advance?: boolean;
}

export interface ScheduleInterviewDto {
  job_stage_id: string;
  onboarding_employee_id: string;
  scheduled_date: string;
  scheduled_time: string;
  location?: string;
  interviewer_name?: string;
  interviewer_email?: string;
  notes?: string;
}

export interface UploadDocumentDto {
  job_stage_id: string;
  onboarding_employee_id: string;
  documents: Array<{
    name: string;
    url: string;
    type: string;
    uploaded_by: string;
  }>;
  admin_notes?: string;
}

@Injectable()
export class JobStageV2Service {
  constructor(
    @InjectRepository(JobStage, 'mysql')
    private readonly jobStageRepository: Repository<JobStage>,

    @InjectRepository(JobStageCompletion, 'mysql')
    private readonly jobStageCompletionRepository: Repository<JobStageCompletion>,

    @InjectRepository(Organization, 'mysql')
    private readonly organizationRepository: Repository<Organization>,

    @Inject(forwardRef(() => OnboardingEmployeeService))
    private readonly onboardingEmployeeService: OnboardingEmployeeService,

    private readonly orgFormsService: OrgFormsRepositoryService,
    private readonly formValueService: FormValueService,
    private readonly sendGridService: SendGridService,
  ) {}

  /**
   * V2: Enhanced form submission handling with automatic stage progression
   * @param form_id string
   * @param user_id string
   * @param form_data any
   * @param organization_id string
   * @returns Promise<any>
   */
  async handleFormSubmissionV2(
    form_id: string,
    user_id: string,
    form_data: any,
    organization_id: string,
  ): Promise<any> {
    try {
      // Get onboarding employee
      const employee = await this.onboardingEmployeeService.findOne(
        { onboarding_employee_id: user_id },
        false,
        { id: true, onboarding_employee_id: true },
        { job: true, organization: true },
      );

      if (!employee || !employee.job) {
        return { message: 'Employee or job not found' };
      }

      // Get current stage completions
      const stageCompletions = await this.getStageProgress(user_id);

      // Find the current active stage
      const currentStage = stageCompletions.find(
        (completion) =>
          completion.status === STAGE_COMPLETION_STATUS.IN_PROGRESS ||
          completion.status === STAGE_COMPLETION_STATUS.NOT_STARTED,
      );

      if (!currentStage) {
        return { message: 'No active stage found' };
      }

      // Check if this form belongs to the current stage
      const stageHasForm = await this.checkFormBelongsToStage(
        currentStage.job_stage.id,
        form_id,
      );

      if (!stageHasForm) {
        return { message: 'Form does not belong to current stage' };
      }

      // Get form details to check if it's a quiz
      const form = await this.orgFormsService.findOne(
        { form_id, organization: organization_id },
        false,
        { is_quiz_form: true, name: true },
      );

      // Handle form submission based on type
      if (form?.is_quiz_form) {
        return await this.handleQuizFormSubmission(
          currentStage,
          form_id,
          form_data,
          employee,
        );
      } else {
        return await this.handleNormalFormSubmission(
          currentStage,
          form_id,
          form_data,
          employee,
        );
      }
    } catch (error) {
      throw new HttpException(
        `Form submission handling failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Handle normal form submission
   */
  private async handleNormalFormSubmission(
    stageCompletion: JobStageCompletion,
    form_id: string,
    form_data: any,
    employee: OnboardingEmployee,
  ): Promise<any> {
    // Update stage to in progress if not started
    if (stageCompletion.status === STAGE_COMPLETION_STATUS.NOT_STARTED) {
      await this.updateStageStatus(
        stageCompletion.completion_id,
        STAGE_COMPLETION_STATUS.IN_PROGRESS,
        COMPLETED_BY_TYPE.APPLICANT,
      );
    }

    // Check if all forms in this stage are completed
    const allFormsCompleted = await this.checkAllStageFormsCompleted(
      stageCompletion.job_stage.id,
      employee.onboarding_employee_id,
    );

    if (allFormsCompleted) {
      // Complete the stage
      await this.completeStageV2({
        job_stage_id: stageCompletion.job_stage.job_stage_id,
        onboarding_employee_id: employee.onboarding_employee_id,
        status: STAGE_COMPLETION_STATUS.COMPLETED,
        completed_by_type: COMPLETED_BY_TYPE.APPLICANT,
        completion_notes: 'All forms completed',
        auto_advance: true,
      });

      // Auto-advance to next stage if applicable
      await this.autoAdvanceToNextStage(employee);
    }

    return {
      message: 'Form submitted successfully',
      stage_updated: allFormsCompleted,
    };
  }

  /**
   * Handle quiz form submission with scoring
   */
  private async handleQuizFormSubmission(
    stageCompletion: JobStageCompletion,
    form_id: string,
    form_data: any,
    employee: OnboardingEmployee,
  ): Promise<any> {
    // Calculate quiz scores (this would integrate with your existing scoring logic)
    const scores = await this.calculateQuizScores(form_id, form_data);

    // Update stage completion with scores
    const updatedCompletion = await this.jobStageCompletionRepository.save({
      ...stageCompletion,
      status: STAGE_COMPLETION_STATUS.IN_PROGRESS,
      form_scores: {
        ...stageCompletion.form_scores,
        [form_id]: scores,
      },
    });

    // Check if all quiz forms in stage are completed
    const allQuizFormsCompleted = await this.checkAllStageQuizFormsCompleted(
      stageCompletion.job_stage.id,
      employee.onboarding_employee_id,
    );

    if (allQuizFormsCompleted) {
      // Calculate total stage score
      const totalStageScore = await this.calculateTotalStageScore(
        stageCompletion.job_stage.id,
        employee.onboarding_employee_id,
      );

      // Check if minimum score is met
      const minScoreRequired =
        stageCompletion.job_stage.min_score_required || 0;
      const passed = totalStageScore.percentage >= minScoreRequired;

      if (passed) {
        await this.completeStageV2({
          job_stage_id: stageCompletion.job_stage.job_stage_id,
          onboarding_employee_id: employee.onboarding_employee_id,
          status: STAGE_COMPLETION_STATUS.COMPLETED,
          completed_by_type: COMPLETED_BY_TYPE.APPLICANT,
          completion_notes: `Quiz completed with score: ${totalStageScore.percentage}%`,
          form_scores: totalStageScore,
          auto_advance: true,
        });

        // Send success email with scores
        await this.sendQuizResultEmail(employee, totalStageScore, true);

        // Auto-advance to next stage
        await this.autoAdvanceToNextStage(employee);
      } else {
        // Failed quiz - mark as pending approval or rejected
        await this.completeStageV2({
          job_stage_id: stageCompletion.job_stage.job_stage_id,
          onboarding_employee_id: employee.onboarding_employee_id,
          status: STAGE_COMPLETION_STATUS.PENDING_APPROVAL,
          completed_by_type: COMPLETED_BY_TYPE.APPLICANT,
          completion_notes: `Quiz failed with score: ${totalStageScore.percentage}%`,
          form_scores: totalStageScore,
          requires_admin_action: true,
        });

        // Send failure email
        await this.sendQuizResultEmail(employee, totalStageScore, false);
      }
    }

    return {
      message: 'Quiz submitted successfully',
      scores,
      stage_completed: allQuizFormsCompleted,
    };
  }

  /**
   * V2: Schedule interview with enhanced data
   */
  async scheduleInterviewV2(
    scheduleDto: ScheduleInterviewDto,
    user: User,
  ): Promise<JobStageCompletion> {
    try {
      const completion = await this.jobStageCompletionRepository.findOne({
        where: {
          job_stage: { job_stage_id: scheduleDto.job_stage_id },
          onboarding_employee: {
            onboarding_employee_id: scheduleDto.onboarding_employee_id,
          },
        },
        relations: { job_stage: true, onboarding_employee: true },
      });

      if (!completion) {
        throw new HttpException(
          'Stage completion not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const scheduledData = {
        date: scheduleDto.scheduled_date,
        time: scheduleDto.scheduled_time,
        location: scheduleDto.location,
        interviewer_name: scheduleDto.interviewer_name,
        interviewer_email: scheduleDto.interviewer_email,
        notes: scheduleDto.notes,
        scheduled_by: user.name,
        scheduled_at: new Date(),
      };

      const updatedCompletion = await this.jobStageCompletionRepository.save({
        ...completion,
        status: STAGE_COMPLETION_STATUS.SCHEDULED,
        scheduled_data: scheduledData,
        scheduled_date: new Date(
          `${scheduleDto.scheduled_date} ${scheduleDto.scheduled_time}`,
        ),
        completed_by_user: user,
        completed_by_type: COMPLETED_BY_TYPE.ADMIN,
        admin_notes: scheduleDto.notes,
      });

      // Send interview scheduled email
      await this.sendInterviewScheduledEmail(
        completion.onboarding_employee,
        scheduledData,
      );

      return updatedCompletion;
    } catch (error) {
      throw new HttpException(
        `Interview scheduling failed: ${error.message}`,
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Complete interview
   */
  async completeInterviewV2(
    job_stage_id: string,
    onboarding_employee_id: string,
    admin_notes: string,
    passed: boolean,
    user: User,
  ): Promise<JobStageCompletion> {
    try {
      const completion = await this.jobStageCompletionRepository.findOne({
        where: {
          job_stage: { job_stage_id },
          onboarding_employee: { onboarding_employee_id },
        },
        relations: { job_stage: true, onboarding_employee: true },
      });

      if (!completion) {
        throw new HttpException(
          'Stage completion not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const status = passed
        ? STAGE_COMPLETION_STATUS.COMPLETED
        : STAGE_COMPLETION_STATUS.REJECTED;

      const updatedCompletion = await this.jobStageCompletionRepository.save({
        ...completion,
        status,
        completed_at: new Date(),
        completed_by_user: user,
        completed_by_type: COMPLETED_BY_TYPE.ADMIN,
        admin_notes,
        rejection_reason: passed ? null : admin_notes,
      });

      if (passed) {
        // Send interview completed email
        await this.sendInterviewCompletedEmail(completion.onboarding_employee);

        // Auto-advance to next stage
        await this.autoAdvanceToNextStage(completion.onboarding_employee);
      } else {
        // Send rejection email
        await this.sendRejectionEmail(
          completion.onboarding_employee,
          admin_notes,
        );
      }

      return updatedCompletion;
    } catch (error) {
      throw new HttpException(
        `Interview completion failed: ${error.message}`,
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * V2: Upload background verification documents
   */
  async uploadBGVDocumentsV2(
    uploadDto: UploadDocumentDto,
    user: User,
  ): Promise<JobStageCompletion> {
    try {
      const completion = await this.jobStageCompletionRepository.findOne({
        where: {
          job_stage: { job_stage_id: uploadDto.job_stage_id },
          onboarding_employee: {
            onboarding_employee_id: uploadDto.onboarding_employee_id,
          },
        },
        relations: { job_stage: true, onboarding_employee: true },
      });

      if (!completion) {
        throw new HttpException(
          'Stage completion not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const updatedCompletion = await this.jobStageCompletionRepository.save({
        ...completion,
        status: STAGE_COMPLETION_STATUS.COMPLETED,
        documents: uploadDto.documents,
        completed_at: new Date(),
        completed_by_user: user,
        completed_by_type: COMPLETED_BY_TYPE.ADMIN,
        admin_notes: uploadDto.admin_notes,
      });

      // Convert employee to user (your existing logic)
      await this.convertEmployeeToUser(completion.onboarding_employee);

      return updatedCompletion;
    } catch (error) {
      throw new HttpException(
        `BGV document upload failed: ${error.message}`,
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get stage progress for an employee
   */
  async getStageProgress(
    onboarding_employee_id: string,
  ): Promise<JobStageCompletion[]> {
    return await this.jobStageCompletionRepository.find({
      where: {
        onboarding_employee: { onboarding_employee_id },
      },
      relations: {
        job_stage: true,
        onboarding_employee: true,
        completed_by_user: true,
      },
      order: { job_stage: { sequence_order: 'ASC' } },
    });
  }

  /**
   * Complete stage with V2 enhancements
   */
  async completeStageV2(
    dto: StageCompletionV2Dto,
  ): Promise<JobStageCompletion> {
    const completion = await this.jobStageCompletionRepository.findOne({
      where: {
        job_stage: { job_stage_id: dto.job_stage_id },
        onboarding_employee: {
          onboarding_employee_id: dto.onboarding_employee_id,
        },
      },
      relations: { job_stage: true, onboarding_employee: true },
    });

    if (!completion) {
      throw new HttpException(
        'Stage completion not found',
        HttpStatus.NOT_FOUND,
      );
    }

    return await this.jobStageCompletionRepository.save({
      ...completion,
      status: dto.status,
      completed_at:
        dto.status === STAGE_COMPLETION_STATUS.COMPLETED
          ? new Date()
          : completion.completed_at,
      completed_by_type: dto.completed_by_type,
      completion_notes: dto.completion_notes,
      completion_data: dto.completion_data,
      form_scores: dto.form_scores || completion.form_scores,
      scheduled_data: dto.scheduled_data || completion.scheduled_data,
      documents: dto.documents || completion.documents,
      admin_notes: dto.admin_notes || completion.admin_notes,
      rejection_reason: dto.rejection_reason,
      auto_advance: dto.auto_advance || false,
    });
  }

  /**
   * Update stage status
   */
  async updateStageStatus(
    completion_id: string,
    status: STAGE_COMPLETION_STATUS,
    completed_by_type: COMPLETED_BY_TYPE,
  ): Promise<JobStageCompletion> {
    const completion = await this.jobStageCompletionRepository.findOne({
      where: { completion_id },
    });

    if (!completion) {
      throw new HttpException(
        'Stage completion not found',
        HttpStatus.NOT_FOUND,
      );
    }

    return await this.jobStageCompletionRepository.save({
      ...completion,
      status,
      completed_by_type,
      started_at:
        status === STAGE_COMPLETION_STATUS.IN_PROGRESS
          ? new Date()
          : completion.started_at,
    });
  }

  /**
   * Auto-advance to next stage
   */
  async autoAdvanceToNextStage(employee: OnboardingEmployee): Promise<void> {
    const stageCompletions = await this.getStageProgress(
      employee.onboarding_employee_id,
    );

    // Find next stage that's not started
    const nextStage = stageCompletions.find(
      (completion) => completion.status === STAGE_COMPLETION_STATUS.NOT_STARTED,
    );

    if (nextStage) {
      // Check if next stage requires admin action
      if (
        nextStage.job_stage.requires_admin_approval ||
        nextStage.job_stage.requires_scheduling ||
        nextStage.job_stage.requires_documents
      ) {
        // Mark as waiting for admin action
        await this.updateStageStatus(
          nextStage.completion_id,
          STAGE_COMPLETION_STATUS.PENDING_APPROVAL,
          COMPLETED_BY_TYPE.SYSTEM,
        );

        // Send notification to admin
        await this.sendAdminNotification(employee, nextStage.job_stage);
      } else {
        // Auto-start next stage
        await this.updateStageStatus(
          nextStage.completion_id,
          STAGE_COMPLETION_STATUS.IN_PROGRESS,
          COMPLETED_BY_TYPE.SYSTEM,
        );

        // Send notification to employee
        await this.sendStageStartedEmail(employee, nextStage.job_stage);
      }
    } else {
      // All stages completed - finalize onboarding
      await this.finalizeOnboarding(employee);
    }
  }

  // Additional helper methods for form checking, scoring, emails, etc.
  private async checkFormBelongsToStage(
    job_stage_id: number,
    form_id: string,
  ): Promise<boolean> {
    const jobStage = await this.jobStageRepository.findOne({
      where: { id: job_stage_id },
    });

    if (!jobStage?.associated_forms) return false;

    return jobStage.associated_forms.some((form) => form.form_id === form_id);
  }

  private async checkAllStageFormsCompleted(
    job_stage_id: number,
    user_id: string,
  ): Promise<boolean> {
    // Implementation would check if all forms in the stage are completed
    // This would integrate with your existing form value service
    return true; // Placeholder
  }

  private async checkAllStageQuizFormsCompleted(
    job_stage_id: number,
    user_id: string,
  ): Promise<boolean> {
    // Implementation would check if all quiz forms in the stage are completed
    return true; // Placeholder
  }

  private async calculateQuizScores(
    form_id: string,
    form_data: any,
  ): Promise<any> {
    // This would integrate with your existing quiz scoring logic
    return {
      total_points: 100,
      scored_points: 85,
      percentage: 85,
    };
  }

  private async calculateTotalStageScore(
    job_stage_id: number,
    user_id: string,
  ): Promise<any> {
    // Calculate total score across all quiz forms in the stage
    return {
      total_points: 200,
      scored_points: 170,
      percentage: 85,
      forms: [],
    };
  }

  private async sendQuizResultEmail(
    employee: OnboardingEmployee,
    scores: any,
    passed: boolean,
  ): Promise<void> {
    // Send quiz result email using your existing email service
  }

  private async sendInterviewScheduledEmail(
    employee: OnboardingEmployee,
    scheduledData: any,
  ): Promise<void> {
    // Send interview scheduled email
  }

  private async sendInterviewCompletedEmail(
    employee: OnboardingEmployee,
  ): Promise<void> {
    // Send interview completed email
  }

  private async sendRejectionEmail(
    employee: OnboardingEmployee,
    reason: string,
  ): Promise<void> {
    // Send rejection email
  }

  private async sendAdminNotification(
    employee: OnboardingEmployee,
    stage: JobStage,
  ): Promise<void> {
    // Send notification to admin for manual stages
  }

  private async sendStageStartedEmail(
    employee: OnboardingEmployee,
    stage: JobStage,
  ): Promise<void> {
    // Send stage started email to employee
  }

  private async convertEmployeeToUser(
    employee: OnboardingEmployee,
  ): Promise<void> {
    // Your existing logic to convert employee to user
  }

  private async finalizeOnboarding(
    employee: OnboardingEmployee,
  ): Promise<void> {
    // Finalize the onboarding process
    await this.onboardingEmployeeService.update(
      employee.onboarding_employee_id,
      {
        status: EMPLOYEE_STATUS.EMPLOYEE_ONBOARD_COMPLETED,
      },
    );
  }
}
