import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ArrayNotEmpty,
  ArrayUnique,
} from 'class-validator';
import { StageActionBy } from 'src/util/enums';
import { OrgAppsExists } from 'src/util/validations/apps.validation';
import { OrgFormsExists } from 'src/util/validations/forms.validations';

export class StageDto {
  @IsNotEmpty()
  @OrgAppsExists()
  app_id: string;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsEnum(StageActionBy)
  action_by: StageActionBy;

  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @OrgFormsExists()
  forms_list: string[];
}

export class UpdateStageDto {
  @IsOptional()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(StageActionBy)
  action_by: StageActionBy;

  @IsArray()
  @ArrayUnique()
  @OrgFormsExists()
  forms_list: string[];
}
