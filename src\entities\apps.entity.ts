import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { User } from './user.entity';
import { Stages } from './stage.entity';
import { Themes } from './themes.entity';
import { Organization } from './organization.entity';
import { IndustryTypes } from './industry-types.entity';
import { IndustryAppProcess } from './industry-app-process.entity';
import { OrgAppConfiguration } from './org-app-configuration.entity';

@Entity()
export class Apps {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true, type: 'varchar' })
  app_id: string;

  @Column({ unique: true, type: 'varchar' })
  app_code: string;

  @Column({ unique: true, type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'boolean', default: true })
  status: boolean;

  @ManyToOne(
    () => IndustryAppProcess,
    (industry_app_process: IndustryAppProcess) => industry_app_process.apps,
    {
      eager: false,
      nullable: true,
    },
  )
  @JoinColumn({ name: 'industry_app_process_id' })
  industry_app_process: IndustryAppProcess;

  @ManyToOne(
    () => IndustryTypes,
    (industry_types: IndustryTypes) => industry_types.apps,
    {
      eager: false,
      nullable: true,
    },
  )
  @JoinColumn({ name: 'industry_type_id' })
  industry_types: IndustryTypes;

  @ManyToMany(() => Organization, (org: Organization) => org.apps)
  organizations: Organization[];

  @OneToMany(
    () => OrgAppConfiguration,
    (orgAppConfiguration) => orgAppConfiguration.app,
  )
  orgAppConfiguration: OrgAppConfiguration[];

  @ManyToMany(() => User, (user: User) => user.apps)
  users: User[];

  @ManyToMany(() => Themes, (theme: Themes) => theme.apps)
  themes: Themes[];

  //HACK: only any[] is working here, need to type cast where we use the organization.stages for type safety
  @OneToMany(() => Stages, (stages) => stages.app)
  stages: any[];

  @CreateDateColumn()
  created_at: string;

  @UpdateDateColumn()
  updated_at: string;

  @DeleteDateColumn()
  deleted_at: string;
}
