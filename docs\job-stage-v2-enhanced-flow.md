# Job Stage V2 Enhanced Flow Documentation

## Overview

The V2 Enhanced Job Stage System builds upon your existing V1 flow while adding sophisticated automation, manual controls, and hybrid workflows. It maintains backward compatibility with your current system while providing enhanced capabilities for complex onboarding processes.

## Key Features

### 🔄 **Hybrid Stage Management**
- **Automatic Stages**: Forms auto-advance on completion
- **Manual Stages**: Require admin intervention (interviews, approvals)
- **Hybrid Stages**: Combination of automatic and manual elements

### 📊 **Enhanced Form Handling**
- **Normal Forms**: Traditional form completion with auto-progression
- **Quiz Forms**: Scoring, minimum thresholds, and conditional advancement
- **Form Association**: Link specific forms to stages with requirements

### 🎯 **Intelligent Automation**
- **Auto-Advancement**: Stages progress automatically based on conditions
- **Admin Notifications**: Alerts for manual intervention required
- **Conditional Logic**: Complex rules for stage progression

## Your Existing V1 Flow (Preserved)

### Current Status Flow:
1. **In Progress** → Form filling starts
2. **Application Completed** → All normal forms done
3. **Interview Scheduled** → Admin schedules interview
4. **Interview Re-Scheduled** → Admin reschedules if needed
5. **Interview Completed** → Admin marks interview done
6. **Orientation Process** → Admin starts, user fills quiz forms
7. **Background Verification** → Admin uploads BGV documents
8. **Employee Onboard Completed** → Convert to user

## V2 Enhanced Flow

### Stage Types

#### 1. **Application Stage** (Automatic)
```json
{
  "stage_type": "automatic",
  "associated_forms": [
    {"form_id": "personal-info", "form_type": "normal", "is_required": true},
    {"form_id": "employment-details", "form_type": "normal", "is_required": true}
  ],
  "auto_advance_conditions": {
    "all_forms_completed": true
  }
}
```

**Flow:**
- User fills forms → Auto-advances to next stage
- Email notifications sent automatically
- Status tracking with form completion percentage

#### 2. **Interview Stage** (Manual)
```json
{
  "stage_type": "manual",
  "requires_scheduling": true,
  "requires_admin_approval": true,
  "notification_templates": {
    "scheduled": "interview_scheduled_template",
    "completed": "interview_completed_template"
  }
}
```

**Flow:**
- Admin schedules interview → Status: `SCHEDULED`
- Admin can reschedule → Status: `RE_SCHEDULED`
- Admin completes interview → Status: `COMPLETED` or `REJECTED`
- Auto-advances to next stage if passed

#### 3. **Orientation Stage** (Hybrid)
```json
{
  "stage_type": "hybrid",
  "requires_admin_approval": true,
  "associated_forms": [
    {"form_id": "company-policies", "form_type": "quiz", "min_score": 80},
    {"form_id": "safety-training", "form_type": "quiz", "min_score": 90}
  ],
  "min_score_required": 85
}
```

**Flow:**
- Admin starts orientation → Status: `IN_PROGRESS`
- User completes quiz forms → Scores calculated
- If score ≥ threshold → Auto-advances
- If score < threshold → Status: `PENDING_APPROVAL`

#### 4. **Background Verification** (Manual)
```json
{
  "stage_type": "manual",
  "requires_documents": true,
  "requires_admin_approval": true
}
```

**Flow:**
- Admin uploads BGV documents → Status: `COMPLETED`
- Employee converted to user automatically

## API Endpoints (V2)

### Form Submission with Auto-Progression
```
POST /job-stages/v2/form-submission
```

**Request:**
```json
{
  "form_id": "personal-info-form",
  "user_id": "employee-uuid",
  "form_data": {
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Form submitted successfully",
  "data": {
    "stage_updated": true,
    "next_stage": "interview",
    "auto_advanced": true
  }
}
```

### Interview Scheduling
```
POST /job-stages/v2/schedule-interview
```

**Request:**
```json
{
  "job_stage_id": "interview-stage-uuid",
  "onboarding_employee_id": "employee-uuid",
  "scheduled_date": "2024-01-20",
  "scheduled_time": "10:00",
  "location": "Conference Room A",
  "interviewer_name": "Jane Smith",
  "interviewer_email": "<EMAIL>",
  "notes": "Technical interview - 1 hour"
}
```

### Interview Completion
```
POST /job-stages/v2/complete-interview
```

**Request:**
```json
{
  "job_stage_id": "interview-stage-uuid",
  "onboarding_employee_id": "employee-uuid",
  "admin_notes": "Candidate performed well in technical assessment",
  "passed": true
}
```

### Start Orientation
```
POST /job-stages/v2/start-orientation
```

**Request:**
```json
{
  "onboarding_employee_id": "employee-uuid",
  "admin_notes": "Starting orientation process"
}
```

### Upload BGV Documents
```
POST /job-stages/v2/upload-bgv-documents
```

**Request:**
```json
{
  "job_stage_id": "bgv-stage-uuid",
  "onboarding_employee_id": "employee-uuid",
  "documents": [
    {
      "name": "Background Check Report",
      "url": "https://storage.com/bgv-report.pdf",
      "type": "background_check",
      "uploaded_by": "admin-uuid"
    }
  ],
  "admin_notes": "Background verification completed successfully"
}
```

### Enhanced Progress Tracking
```
GET /job-stages/v2/progress/{employee_id}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "job_stage": {
        "title": "Application Review",
        "stage_type": "automatic",
        "sequence_order": 1
      },
      "status": "COMPLETED",
      "form_scores": null,
      "stage_metadata": {
        "is_manual": false,
        "has_forms": true,
        "requires_admin": false
      },
      "completion_metadata": {
        "has_scores": false,
        "needs_admin_action": false
      }
    },
    {
      "job_stage": {
        "title": "Technical Interview",
        "stage_type": "manual",
        "sequence_order": 2
      },
      "status": "SCHEDULED",
      "scheduled_data": {
        "date": "2024-01-20",
        "time": "10:00",
        "interviewer_name": "Jane Smith"
      },
      "stage_metadata": {
        "is_manual": true,
        "requires_scheduling": true,
        "requires_admin": true
      }
    }
  ]
}
```

## Integration with Existing V1 System

### Backward Compatibility
- V1 endpoints remain unchanged
- V1 status enums preserved
- Existing employee records work seamlessly

### Migration Strategy
1. **Gradual Migration**: New jobs use V2, existing continue with V1
2. **Feature Flags**: Enable V2 features per organization
3. **Data Preservation**: All existing data remains intact

### V1 to V2 Mapping
```
V1 Status                    → V2 Status
"In Progress"               → "IN_PROGRESS"
"Application Completed"     → "COMPLETED" (Application Stage)
"Interview Scheduled"       → "SCHEDULED" (Interview Stage)
"Interview Re Scheduled"    → "RE_SCHEDULED" (Interview Stage)
"Interview Completed"       → "COMPLETED" (Interview Stage)
"Orientation..."           → Various orientation statuses
"Employee Onboard..."      → "COMPLETED" (All stages)
```

## Enhanced Features

### 1. **Smart Notifications**
- Stage-specific email templates
- Admin action alerts
- Employee progress updates
- Reminder notifications

### 2. **Scoring System**
- Quiz form scoring with thresholds
- Aggregate stage scores
- Pass/fail logic with admin override
- Detailed score reporting

### 3. **Document Management**
- Structured document uploads
- Document type categorization
- Admin notes and approval tracking
- Audit trail for compliance

### 4. **Admin Dashboard**
- Pending actions overview
- Bulk operations support
- Progress analytics
- Bottleneck identification

### 5. **Conditional Logic**
- Complex advancement rules
- Score-based progression
- Time-based constraints
- Custom business logic

## Benefits of V2 System

### ✅ **For Administrators**
- **Reduced Manual Work**: Automatic progression where possible
- **Better Visibility**: Enhanced tracking and reporting
- **Flexible Control**: Manual override capabilities
- **Bulk Operations**: Handle multiple candidates efficiently

### ✅ **For Candidates**
- **Clear Progress**: Visual stage progression
- **Immediate Feedback**: Real-time status updates
- **Better Communication**: Automated notifications
- **Transparent Process**: Clear expectations

### ✅ **For Organizations**
- **Scalability**: Handle high-volume hiring
- **Consistency**: Standardized processes
- **Compliance**: Audit trails and documentation
- **Analytics**: Data-driven insights

This V2 system provides the sophistication you need while preserving your existing investment in the V1 system! 🎉
