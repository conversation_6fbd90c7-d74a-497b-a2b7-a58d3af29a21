import { Module } from '@nestjs/common';
import { UserModule } from '../user/user.module';
import { AppsModule } from '../apps/apps.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import OnBoardEmpChecklistController from './onboard-emp-checklist-repository.controller';
import { User } from 'src/entities/user.entity';
import { Organization } from 'src/entities/organization.entity';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import {
  FormsRepository,
  FormsRepositorySchema,
} from 'src/entities/mongodb/formsrepository.entity';
import {
  FormValuesRepository,
  FormValuesRepositorySchema,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import {
  ClientsRepository,
  ClientsRepositorySchema,
} from 'src/entities/mongodb/clientsrepository.entity';
import {
  FormFields,
  FormFieldsSchema,
} from 'src/entities/mongodb/formfields.entity';
import {
  FormSectionsRepository,
  FormSectionsRepositorySchema,
} from 'src/entities/mongodb/form-sections.entity';
import { OrganizationService } from 'src/services/organization.service';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { IndustryTypesService } from 'src/services/industry-types.service';
import {
  OrgFormsRepository,
  OrgFormsRepositorySchema,
} from 'src/entities/mongodb/orgformsrepository.entity';
import { FormsRepositoryService } from 'src/services/formsrespository.service';
import { GlobalService } from 'src/services/global.service';
import { FormFieldsService } from 'src/services/formfields.service';
import { FormSectionsRepositoryService } from 'src/services/form-sections.service';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import { FormValueService } from 'src/services/formvalues.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { SendGridService } from 'src/util/sendgrid.service';
import { Configurations } from 'src/entities/configurations.entity';
import {
  ESignRequestLogsRepository,
  ESignRequestLogsSchema,
} from 'src/entities/mongodb/e-sign-request-log.entity';
import { ConfigurationsModule } from '../configurations/configurations.module';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import EncryptionService from 'src/services/encryption.service';
import {
  PrimaryFormsRepository,
  PrimaryFormsRepositorySchema,
} from 'src/entities/mongodb/primary-forms-repository.entity';
import {
  AppOrgFormsToggleRepository,
  AppOrgFormsToggleRepositorySchema,
} from 'src/entities/mongodb/app-org-forms-toggle.entity';
import { CaregiversModule } from '../caregivers/caregivers.module';
import { JobStageService } from 'src/services/job-stage.service';
import { JobVersionService } from 'src/services/job-version.service';
import { JobService } from 'src/services/job.service';
import { JobStageCompletion } from 'src/entities/job-stage-completion.entity';
import { JobStage } from 'src/entities/job-stage.entity';
import { JobVersion } from 'src/entities/job-version.entity';
import { Job } from 'src/entities/job.entity';
import { Stages } from 'src/entities/stage.entity';

@Module({
  imports: [
    UserModule,
    AppsModule,
    TypeOrmModule.forFeature(
      [
        OnboardingEmployee,
        User,
        Organization,
        IndustryTypes,
        OrgAppConfiguration,
        Configurations,
        IndustryAppProcess,
        Job,
        JobVersion,
        JobStage,
        JobStageCompletion,
        Stages,
      ],
      'mysql',
    ),
    CaregiversModule,
    MongooseModule.forFeature([
      {
        name: OnBoardEmpChecklistRepository.name,
        schema: OnBoardEmpChecklistRepositorySchema,
      },
      { name: FormsRepository.name, schema: FormsRepositorySchema },
      { name: FormValuesRepository.name, schema: FormValuesRepositorySchema },
      { name: ClientsRepository.name, schema: ClientsRepositorySchema },
      { name: FormFields.name, schema: FormFieldsSchema },
      { name: ESignRequestLogsRepository.name, schema: ESignRequestLogsSchema },
      {
        name: PrimaryFormsRepository.name,
        schema: PrimaryFormsRepositorySchema,
      },
      {
        name: AppOrgFormsToggleRepository.name,
        schema: AppOrgFormsToggleRepositorySchema,
      },
      {
        name: FormSectionsRepository.name,
        schema: FormSectionsRepositorySchema,
      },
      { name: OrgFormsRepository.name, schema: OrgFormsRepositorySchema },
      {
        name: OnBoardOrgChecklistRepository.name,
        schema: OnBoardOrgChecklistRepositorySchema,
      },
    ]),
    ConfigurationsModule,
  ],
  exports: [],
  providers: [
    OnBoardEmpChecklistService,
    OrganizationService,
    OrgFormsRepositoryService,
    IndustryTypesService,
    FormsRepositoryService,
    GlobalService,
    FormFieldsService,
    FormSectionsRepositoryService,
    OnboardingEmployeeService,
    OnBoardOrgChecklistService,
    FormValueService,
    ClientsRepositoryService,
    SendGridService,
    EncryptionService,
    JobService,
    JobVersionService,
    JobStageService,
  ],
  controllers: [OnBoardEmpChecklistController],
})
export class OnBoardEmpChecklistModule {}
