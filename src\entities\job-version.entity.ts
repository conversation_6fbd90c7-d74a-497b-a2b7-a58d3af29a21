import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';

import { Organization } from './organization.entity';
import { Job, EMPLOYMENT_TYPE, JOB_STATUS } from './job.entity';
import { OnboardingEmployee } from './onboarding-employee.entity';

@Entity()
export class JobVersion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  job_version_id: string;

  @ManyToOne(() => Job, { eager: false, nullable: false })
  @JoinColumn()
  job: Job;

  @Column()
  version: number;

  @Column()
  title: string;

  @Column({ nullable: true })
  department: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  requirements: string;

  @Column({ nullable: true })
  salary_range: string;

  @Column({
    type: 'enum',
    enum: EMPLOYMENT_TYPE,
    default: EMPLOYMENT_TYPE.FULL_TIME,
  })
  employment_type: EMPLOYMENT_TYPE;

  @Column({ nullable: true })
  location: string;

  @Column({
    type: 'enum',
    enum: JOB_STATUS,
    default: JOB_STATUS.ACTIVE,
  })
  status: JOB_STATUS;

  @Column({ type: 'date', nullable: true })
  application_start_date: Date;

  @Column({ type: 'date', nullable: true })
  application_end_date: Date;

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  @OneToMany(
    () => OnboardingEmployee,
    (onboardingEmployee: OnboardingEmployee) => onboardingEmployee.job_version,
  )
  onboarding_employees: OnboardingEmployee[];

  @Column({ type: 'text', nullable: true })
  change_reason: string;

  @Column({ type: 'boolean', default: false })
  is_current_version: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}
