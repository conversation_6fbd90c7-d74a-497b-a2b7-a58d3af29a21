import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Req,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthGuard } from '@nestjs/passport';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { JobStageService } from 'src/services/job-stage.service';
import { OrganizationService } from 'src/services/organization.service';
import { JobService } from 'src/services/job.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import {
  AssignJobStagesDto,
  CompleteStageDto,
  UpdateStageCompletionDto,
  JobStageProgressDto,
} from 'src/dto/job-stage.dto';

@Controller('job-stages')
@UseGuards(AuthGuard('jwt'), OrganizationAuth)
export default class JobStageController {
  constructor(
    private readonly jobStageService: JobStageService,
    private readonly organizationService: OrganizationService,
    private readonly jobService: JobService,
    private readonly onboardingEmployeeService: OnboardingEmployeeService,
  ) {}

  /**
   * Assign stages to a job
   * @param req Request
   * @param res Response
   * @param assignJobStagesDto AssignJobStagesDto
   */
  @Post('assign')
  @UsePipes(new ValidationPipe())
  async assignStagesToJob(
    @Req() req: Request,
    @Res() res: Response,
    @Body() assignJobStagesDto: AssignJobStagesDto,
  ) {
    try {
      const organization = await this.organizationService.findOne({
        organization_id: req.headers.organization as string,
      });

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const job = await this.jobService.findOne(
        { job_id: assignJobStagesDto.job_id },
        false,
        { id: true, job_id: true },
      );

      if (!job) {
        throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
      }

      const jobStages = await this.jobStageService.assignStagesToJob(
        job,
        assignJobStagesDto.stages,
        organization,
      );

      return res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'Stages assigned to job successfully',
        data: jobStages,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get job stages for a job
   * @param req Request
   * @param res Response
   * @param job_id string
   */
  @Get('job/:job_id')
  async getJobStages(
    @Req() req: Request,
    @Res() res: Response,
    @Param('job_id') job_id: string,
  ) {
    try {
      const jobStages = await this.jobStageService.getJobStages(job_id);

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Job stages retrieved successfully',
        data: jobStages,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Complete a stage
   * @param req Request
   * @param res Response
   * @param completeStageDto CompleteStageDto
   */
  @Post('complete')
  @UsePipes(new ValidationPipe())
  async completeStage(
    @Req() req: Request,
    @Res() res: Response,
    @Body() completeStageDto: CompleteStageDto,
  ) {
    try {
      const user = req.user as any;
      const completion = await this.jobStageService.completeStage(
        completeStageDto,
        user,
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Stage completed successfully',
        data: completion,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get stage progress for an employee
   * @param req Request
   * @param res Response
   * @param onboarding_employee_id string
   */
  @Get('progress/:onboarding_employee_id')
  async getStageProgress(
    @Req() req: Request,
    @Res() res: Response,
    @Param('onboarding_employee_id') onboarding_employee_id: string,
  ) {
    try {
      const progress = await this.jobStageService.getStageProgress(
        onboarding_employee_id,
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Stage progress retrieved successfully',
        data: progress,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Initialize stage completions for an employee
   * @param req Request
   * @param res Response
   * @param onboarding_employee_id string
   */
  @Post('initialize/:onboarding_employee_id')
  async initializeStageCompletions(
    @Req() req: Request,
    @Res() res: Response,
    @Param('onboarding_employee_id') onboarding_employee_id: string,
  ) {
    try {
      const onboardingEmployee = await this.onboardingEmployeeService.findOne(
        { onboarding_employee_id },
        false,
        { id: true, onboarding_employee_id: true },
        { job: true },
      );

      if (!onboardingEmployee) {
        throw new HttpException(
          'Onboarding employee not found',
          HttpStatus.NOT_FOUND,
        );
      }

      const completions = await this.jobStageService.initializeStageCompletions(
        onboardingEmployee,
      );

      return res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'Stage completions initialized successfully',
        data: completions,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update stage completion
   * @param req Request
   * @param res Response
   * @param completion_id string
   * @param updateDto UpdateStageCompletionDto
   */
  @Put('completion/:completion_id')
  @UsePipes(new ValidationPipe())
  async updateStageCompletion(
    @Req() req: Request,
    @Res() res: Response,
    @Param('completion_id') completion_id: string,
    @Body() updateDto: UpdateStageCompletionDto,
  ) {
    try {
      const completion = await this.jobStageService.updateStageCompletion(
        completion_id,
        updateDto,
      );

      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Stage completion updated successfully',
        data: completion,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
