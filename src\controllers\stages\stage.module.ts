import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import StageController from './stages.controller';
import { Stages } from 'src/entities/stage.entity';
import { OrganizationService } from 'src/services/organization.service';
import { OrganizationModule } from '../organization/organization.module';
import { Organization } from 'src/entities/organization.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { Configurations } from 'src/entities/configurations.entity';
import { User } from 'src/entities/user.entity';
import { AppsService } from 'src/services/apps.service';
import { AppsModule } from '../apps/apps.module';
import { Apps } from 'src/entities/apps.entity';
import StageService from 'src/services/stages.service';

const services = [OrganizationService, AppsService, StageService];
@Module({
  controllers: [StageController],
  imports: [
    TypeOrmModule.forFeature(
      [Stages, Organization, OrgAppConfiguration, Configurations, User, Apps],
      'mysql',
    ),
    OrganizationModule,
    AppsModule,
  ],
  providers: services,
  exports: services,
})
export default class StageModule {}
