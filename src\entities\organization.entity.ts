import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToOne,
  JoinTable,
  PrimaryColumn,
  ManyToMany,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Apps } from './apps.entity';
import { IndustryTypes } from './industry-types.entity';
import { OrgAppConfiguration } from './org-app-configuration.entity';
import { User } from './user.entity';
import { Stages } from './stage.entity';

@Entity()
export class Organization {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  organization_id: string;

  @Column({ unique: true })
  name: string;

  @Column({ unique: true })
  email: string;

  @Column({ unique: true })
  mobile_number: string;

  @Column({ unique: true, nullable: true })
  pass_key: string;

  @Column()
  password: string;

  @Column({ type: 'longtext', nullable: true })
  logo: string;

  @Column({ type: Boolean, default: true })
  status: boolean;

  @ManyToOne(() => IndustryTypes, (industry_types) => industry_types.apps, {
    eager: false,
    nullable: true,
  })
  @JoinColumn({ name: 'industry_type_id' })
  industry_type: IndustryTypes;

  @ManyToMany(() => Apps, (app: Apps) => app.organizations, {
    eager: false,
    nullable: false,
  })
  @JoinTable({
    name: 'organization_apps',
    joinColumn: {
      name: 'organization_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'app_id',
      referencedColumnName: 'id',
    },
  })
  apps: Apps[];

  @OneToMany(
    () => OrgAppConfiguration,
    (orgAppConfiguration) => orgAppConfiguration.organization,
  )
  orgAppConfiguration: OrgAppConfiguration[];

  @OneToMany(() => User, (user) => user.organization)
  user: User[];

  //HACK: only any[] is working here, need to type cast where we use the organization.stages for type safety
  @OneToMany(() => Stages, (stage) => stage.organization)
  stages: any[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}

@Entity('organization_apps')
export class OrganizationApps {
  @PrimaryColumn({ type: 'int' })
  app_id: number;

  @PrimaryColumn({ type: 'int' })
  organization_id: number;

  @OneToOne(() => Organization)
  @JoinTable()
  organization: Organization;

  @OneToOne(() => Apps)
  @JoinTable()
  app: Apps;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}
