import { TypeOrmModule } from '@nestjs/typeorm';
import { Module, forwardRef } from '@nestjs/common';

import { Apps } from 'src/entities/apps.entity';
import { AppsController } from './apps.controller';
import { AdminModule } from '../admin/admin.module';
import { AppsService } from 'src/services/apps.service';
import {
  AppNameExistRule,
  AppsExistRule,
} from 'src/util/validations/apps.validation';
import { FormsRepositoryModule } from '../formsrepository/formsrepository.module';
import { OrganizationModule } from '../organization/organization.module';
import { Organization } from 'src/entities/organization.entity';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { IndustryTypesExistsRule } from 'src/util/validations/industry-types.validation';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { IndustryAppProcessService } from 'src/services/industry-app-process.service';
import { IndustryAppProcessExistsRule } from 'src/util/validations/industry-app-process.validation';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { PaginationService } from 'src/util/pagination.service';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        Apps,
        Organization,
        IndustryTypes,
        IndustryAppProcess,
        OrgAppConfiguration,
      ],
      'mysql',
    ),
    AdminModule,
    forwardRef(() => OrganizationModule),
    forwardRef(() => FormsRepositoryModule),
  ],
  controllers: [AppsController],
  providers: [
    AppsService,
    AppsExistRule,
    AppNameExistRule,
    IndustryTypesExistsRule,
    IndustryAppProcessExistsRule,
    IndustryTypesService,
    IndustryAppProcessService,
    PaginationService,
  ],
  exports: [AppsService, IndustryAppProcessService, PaginationService],
})
export class AppsModule {}
