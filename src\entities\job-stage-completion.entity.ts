import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';

import { generateUUID } from 'src/util';
import { Stages } from './stage.entity';
import { Job } from './job.entity';
import { OnboardingEmployee } from './onboarding-employee.entity';
import { User } from './user.entity';
import { Organization } from './organization.entity';

export enum STAGE_COMPLETION_STATUS {
  NOT_STARTED = 'Not Started',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
  SKIPPED = 'Skipped',
}

export enum COMPLETED_BY_TYPE {
  APPLICANT = 'applicant',
  USER = 'user',
  ADMIN = 'admin',
  SYSTEM = 'system',
}

@Entity()
export class JobStageCompletion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  completion_id: string;

  @ManyToOne(() => Job, { eager: false, nullable: false })
  @JoinColumn()
  job: Job;

  @ManyToOne(() => Stages, { eager: false, nullable: false })
  @JoinColumn()
  stage: Stages;

  @Column({ type: 'int', default: 1 })
  sequence_order: number;

  @ManyToOne(
    () => OnboardingEmployee,
    (employee) => employee.stage_completions,
    {
      eager: false,
      nullable: false,
    },
  )
  @JoinColumn()
  onboarding_employee: OnboardingEmployee;

  @Column({
    type: 'enum',
    enum: STAGE_COMPLETION_STATUS,
    default: STAGE_COMPLETION_STATUS.NOT_STARTED,
  })
  status: STAGE_COMPLETION_STATUS;

  @Column({ type: 'datetime', nullable: true })
  started_at: Date;

  @Column({ type: 'datetime', nullable: true })
  completed_at: Date;

  @ManyToOne(() => User, { eager: false, nullable: true })
  @JoinColumn()
  completed_by_user: User;

  @Column({
    type: 'enum',
    enum: COMPLETED_BY_TYPE,
    nullable: true,
  })
  completed_by_type: COMPLETED_BY_TYPE;

  @Column({ type: 'text', nullable: true })
  completion_notes: string;

  @Column({ type: 'json', nullable: true })
  completion_data: any;

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;

  @BeforeInsert()
  generateCompletionId() {
    this.completion_id = generateUUID();
  }
}
