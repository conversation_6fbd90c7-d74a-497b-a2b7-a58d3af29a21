import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  Join<PERSON>olumn,
  BeforeInsert,
} from 'typeorm';

import { generateUUID } from 'src/util';
import { JobStage } from './job-stage.entity';
import { OnboardingEmployee } from './onboarding-employee.entity';
import { User } from './user.entity';
import { Organization } from './organization.entity';

export enum STAGE_COMPLETION_STATUS {
  NOT_STARTED = 'Not Started',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
  SKIPPED = 'Skipped',
  // V2 Enhanced statuses
  SCHEDULED = 'Scheduled',
  RE_SCHEDULED = 'Re-Scheduled',
  PENDING_APPROVAL = 'Pending Approval',
  APPROVED = 'Approved',
  REJECTED = 'Rejected',
  WAITING_FOR_DOCUMENTS = 'Waiting for Documents',
}

export enum COMPLETED_BY_TYPE {
  APPLICANT = 'applicant',
  USER = 'user',
  ADMIN = 'admin',
  SYSTEM = 'system',
}

export enum STAGE_TYPE {
  AUTOMATIC = 'automatic',
  MANUAL = 'manual',
  HYBRID = 'hybrid',
}

export enum FORM_TYPE {
  NORMAL = 'normal',
  QUIZ = 'quiz',
}

@Entity()
export class JobStageCompletion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  completion_id: string;

  @ManyToOne(() => JobStage, (jobStage) => jobStage.completions, {
    eager: false,
    nullable: false,
  })
  @JoinColumn()
  job_stage: JobStage;

  @ManyToOne(
    () => OnboardingEmployee,
    (employee) => employee.stage_completions,
    {
      eager: false,
      nullable: false,
    },
  )
  @JoinColumn()
  onboarding_employee: OnboardingEmployee;

  @Column({
    type: 'enum',
    enum: STAGE_COMPLETION_STATUS,
    default: STAGE_COMPLETION_STATUS.NOT_STARTED,
  })
  status: STAGE_COMPLETION_STATUS;

  @Column({ type: 'datetime', nullable: true })
  started_at: Date;

  @Column({ type: 'datetime', nullable: true })
  completed_at: Date;

  @ManyToOne(() => User, { eager: false, nullable: true })
  @JoinColumn()
  completed_by_user: User;

  @Column({
    type: 'enum',
    enum: COMPLETED_BY_TYPE,
    nullable: true,
  })
  completed_by_type: COMPLETED_BY_TYPE;

  @Column({ type: 'text', nullable: true })
  completion_notes: string;

  @Column({ type: 'json', nullable: true })
  completion_data: any;

  // V2 Enhanced fields
  @Column({ type: 'json', nullable: true })
  form_scores: any; // For quiz forms: {total_points, scored_points, forms: [{form_id, score}]}

  @Column({ type: 'json', nullable: true })
  scheduled_data: any; // For interviews: {date, time, location, interviewer}

  @Column({ type: 'json', nullable: true })
  documents: any; // For document uploads: [{name, url, type, uploaded_by}]

  @Column({ type: 'text', nullable: true })
  admin_notes: string; // Notes from admin/interviewer

  @Column({ type: 'text', nullable: true })
  rejection_reason: string; // Reason for rejection

  @Column({ type: 'boolean', default: false })
  requires_admin_action: boolean; // Whether this stage needs admin intervention

  @Column({ type: 'boolean', default: false })
  auto_advance: boolean; // Whether to auto-advance to next stage

  @Column({ type: 'json', nullable: true })
  notification_data: any; // Email notification tracking

  @Column({ type: 'datetime', nullable: true })
  scheduled_date: Date; // For scheduled events

  @Column({ type: 'datetime', nullable: true })
  due_date: Date; // When this stage should be completed

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;

  @BeforeInsert()
  generateCompletionId() {
    this.completion_id = generateUUID();
  }
}
