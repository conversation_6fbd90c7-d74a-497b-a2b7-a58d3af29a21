import {
  IsNotEmpty,
  IsOptional,
  IsString,
  <PERSON><PERSON><PERSON>y,
  IsEnum,
} from 'class-validator';
import {
  STAGE_COMPLETION_STATUS,
  COMPLETED_BY_TYPE,
} from 'src/entities/job-stage-completion.entity';

export class AssignJobStagesDto {
  @IsNotEmpty()
  @IsString()
  job_id: string;

  @IsArray()
  @IsString({ each: true })
  stage_ids: string[];
}

export class CompleteStageDto {
  @IsNotEmpty()
  @IsString()
  job_id: string;

  @IsNotEmpty()
  @IsString()
  stage_id: string;

  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsOptional()
  @IsEnum(STAGE_COMPLETION_STATUS)
  status?: STAGE_COMPLETION_STATUS;

  @IsOptional()
  @IsEnum(COMPLETED_BY_TYPE)
  completed_by_type?: COMPLETED_BY_TYPE;

  @IsOptional()
  @IsString()
  completion_notes?: string;

  @IsOptional()
  completion_data?: any;
}

export class UpdateStageCompletionDto {
  @IsOptional()
  @IsEnum(STAGE_COMPLETION_STATUS)
  status?: STAGE_COMPLETION_STATUS;

  @IsOptional()
  @IsString()
  completion_notes?: string;

  @IsOptional()
  completion_data?: any;
}

export class JobStageProgressDto {
  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsOptional()
  @IsString()
  job_id?: string;
}
