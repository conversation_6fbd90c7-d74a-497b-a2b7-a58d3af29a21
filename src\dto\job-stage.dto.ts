import {
  IsNotEmpty,
  <PERSON><PERSON>ptional,
  IsString,
  IsArray,
  IsEnum,
  IsNumber,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  STAGE_COMPLETION_STATUS,
  COMPLETED_BY_TYPE,
} from 'src/entities/job-stage-completion.entity';

export class CreateJobStageDto {
  @IsNotEmpty()
  @IsString()
  stage_id: string;

  @IsOptional()
  @IsNumber()
  sequence_order?: number;

  @IsOptional()
  @IsBoolean()
  is_required?: boolean;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;
}

export class AssignJobStagesDto {
  @IsNotEmpty()
  @IsString()
  job_id: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateJobStageDto)
  stages: CreateJobStageDto[];
}

export class CompleteStageDto {
  @IsNotEmpty()
  @IsString()
  job_stage_id: string;

  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsOptional()
  @IsEnum(STAGE_COMPLETION_STATUS)
  status?: STAGE_COMPLETION_STATUS;

  @IsOptional()
  @IsEnum(COMPLETED_BY_TYPE)
  completed_by_type?: COMPLETED_BY_TYPE;

  @IsOptional()
  @IsString()
  completion_notes?: string;

  @IsOptional()
  completion_data?: any;
}

export class UpdateStageCompletionDto {
  @IsOptional()
  @IsEnum(STAGE_COMPLETION_STATUS)
  status?: STAGE_COMPLETION_STATUS;

  @IsOptional()
  @IsString()
  completion_notes?: string;

  @IsOptional()
  completion_data?: any;
}

export class JobStageProgressDto {
  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsOptional()
  @IsString()
  job_id?: string;
}
