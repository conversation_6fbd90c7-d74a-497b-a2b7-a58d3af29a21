import {
  Is<PERSON><PERSON><PERSON>,
  IsString,
  IsBoolean,
  Is<PERSON>rray,
  IsObject,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsDateString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { 
  STAGE_COMPLETION_STATUS, 
  COMPLETED_BY_TYPE, 
  STAGE_TYPE 
} from 'src/entities/job-stage-completion.entity';

// V2 Enhanced DTOs

export class FormSubmissionV2Dto {
  @IsNotEmpty()
  @IsString()
  form_id: string;

  @IsNotEmpty()
  @IsString()
  user_id: string;

  @IsNotEmpty()
  @IsObject()
  form_data: any;
}

export class StageCompletionV2Dto {
  @IsNotEmpty()
  @IsString()
  job_stage_id: string;

  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsNotEmpty()
  @IsEnum(STAGE_COMPLETION_STATUS)
  status: STAGE_COMPLETION_STATUS;

  @IsNotEmpty()
  @IsEnum(COMPLETED_BY_TYPE)
  completed_by_type: COMPLETED_BY_TYPE;

  @IsOptional()
  @IsString()
  completion_notes?: string;

  @IsOptional()
  @IsObject()
  completion_data?: any;

  // V2 Enhanced fields
  @IsOptional()
  @IsObject()
  form_scores?: any;

  @IsOptional()
  @IsObject()
  scheduled_data?: any;

  @IsOptional()
  @IsArray()
  documents?: any[];

  @IsOptional()
  @IsString()
  admin_notes?: string;

  @IsOptional()
  @IsString()
  rejection_reason?: string;

  @IsOptional()
  @IsBoolean()
  auto_advance?: boolean;
}

export class ScheduleInterviewDto {
  @IsNotEmpty()
  @IsString()
  job_stage_id: string;

  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsNotEmpty()
  @IsDateString()
  scheduled_date: string;

  @IsNotEmpty()
  @IsString()
  scheduled_time: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsString()
  interviewer_name?: string;

  @IsOptional()
  @IsString()
  interviewer_email?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class CompleteInterviewV2Dto {
  @IsNotEmpty()
  @IsString()
  job_stage_id: string;

  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsNotEmpty()
  @IsString()
  admin_notes: string;

  @IsNotEmpty()
  @IsBoolean()
  passed: boolean;
}

export class DocumentUploadDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  url: string;

  @IsNotEmpty()
  @IsString()
  type: string;

  @IsNotEmpty()
  @IsString()
  uploaded_by: string;
}

export class UploadDocumentDto {
  @IsNotEmpty()
  @IsString()
  job_stage_id: string;

  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DocumentUploadDto)
  documents: DocumentUploadDto[];

  @IsOptional()
  @IsString()
  admin_notes?: string;
}

export class StartOrientationV2Dto {
  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsOptional()
  @IsString()
  admin_notes?: string;
}

export class AssignFormsToStageDto {
  @IsNotEmpty()
  @IsString()
  job_stage_id: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StageFormDto)
  forms: StageFormDto[];
}

export class StageFormDto {
  @IsNotEmpty()
  @IsString()
  form_id: string;

  @IsNotEmpty()
  @IsString()
  form_type: 'normal' | 'quiz';

  @IsOptional()
  @IsBoolean()
  is_required?: boolean;

  @IsOptional()
  @IsNumber()
  min_score?: number; // For quiz forms
}

export class CreateJobStageV2Dto {
  @IsNotEmpty()
  @IsString()
  job_id: string;

  @IsNotEmpty()
  @IsString()
  stage_id: string; // Global stage to clone from

  @IsOptional()
  @IsNumber()
  sequence_order?: number;

  @IsOptional()
  @IsBoolean()
  is_required?: boolean;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  // V2 Enhanced fields
  @IsOptional()
  @IsEnum(STAGE_TYPE)
  stage_type?: STAGE_TYPE;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StageFormDto)
  associated_forms?: StageFormDto[];

  @IsOptional()
  @IsBoolean()
  requires_scheduling?: boolean;

  @IsOptional()
  @IsBoolean()
  requires_documents?: boolean;

  @IsOptional()
  @IsBoolean()
  requires_admin_approval?: boolean;

  @IsOptional()
  @IsObject()
  auto_advance_conditions?: any;

  @IsOptional()
  @IsObject()
  notification_templates?: any;

  @IsOptional()
  @IsNumber()
  min_score_required?: number;

  @IsOptional()
  @IsNumber()
  max_duration_days?: number;
}

export class BulkStageActionDto {
  @IsArray()
  @IsString({ each: true })
  completion_ids: string[];

  @IsNotEmpty()
  @IsEnum(STAGE_COMPLETION_STATUS)
  action: STAGE_COMPLETION_STATUS;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class StageProgressFilterDto {
  @IsOptional()
  @IsString()
  job_id?: string;

  @IsOptional()
  @IsEnum(STAGE_COMPLETION_STATUS)
  status?: STAGE_COMPLETION_STATUS;

  @IsOptional()
  @IsBoolean()
  requires_admin_action?: boolean;

  @IsOptional()
  @IsDateString()
  due_date_before?: string;

  @IsOptional()
  @IsDateString()
  created_after?: string;
}

export class QuizScoreDto {
  @IsNotEmpty()
  @IsString()
  form_id: string;

  @IsNotEmpty()
  @IsNumber()
  total_points: number;

  @IsNotEmpty()
  @IsNumber()
  scored_points: number;

  @IsNotEmpty()
  @IsNumber()
  percentage: number;

  @IsOptional()
  @IsArray()
  question_scores?: any[];
}

export class StageNotificationDto {
  @IsNotEmpty()
  @IsString()
  onboarding_employee_id: string;

  @IsNotEmpty()
  @IsString()
  job_stage_id: string;

  @IsNotEmpty()
  @IsString()
  notification_type: 'stage_started' | 'stage_completed' | 'admin_action_required' | 'reminder';

  @IsOptional()
  @IsObject()
  template_data?: any;
}
