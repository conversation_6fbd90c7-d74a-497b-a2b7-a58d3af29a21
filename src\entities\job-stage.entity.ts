import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  BeforeInsert,
} from 'typeorm';

import { generateUUID } from 'src/util';
import { Job } from './job.entity';
import { Stages } from './stage.entity';
import { Organization } from './organization.entity';
import { JobStageCompletion } from './job-stage-completion.entity';
import { STAGE_TYPE, FORM_TYPE } from './job-stage-completion.entity';
import { StageActionBy } from 'src/util/enums';

@Entity()
export class JobStage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  job_stage_id: string;

  @ManyToOne(() => Job, (job) => job.job_stages, {
    eager: false,
    nullable: false,
  })
  @JoinColumn()
  job: Job;

  @ManyToOne(() => Stages, { eager: false, nullable: true })
  @JoinColumn()
  parent_stage: Stages;

  @Column({ type: 'int', default: 1 })
  sequence_order: number;

  @Column({ type: 'boolean', default: true })
  is_required: boolean;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  // Cloned stage data from parent stage at time of assignment
  @Column({ type: 'varchar' })
  title: string;

  @Column({ type: 'longtext', nullable: true })
  description: string;

  @Column({ type: 'json', nullable: true })
  forms_list: string[];

  @Column({
    type: 'enum',
    enum: Object.values(StageActionBy),
  })
  action_by: StageActionBy;

  // Version tracking
  @Column({ type: 'int', default: 1 })
  stage_version: number;

  @Column({ type: 'datetime', nullable: true })
  cloned_at: Date;

  // V2 Enhanced fields
  @Column({
    type: 'enum',
    enum: STAGE_TYPE,
    default: STAGE_TYPE.AUTOMATIC,
  })
  stage_type: STAGE_TYPE; // automatic, manual, hybrid

  @Column({ type: 'json', nullable: true })
  associated_forms: any; // [{form_id, form_type: 'normal'|'quiz', is_required: true}]

  @Column({ type: 'boolean', default: false })
  requires_scheduling: boolean; // For interview stages

  @Column({ type: 'boolean', default: false })
  requires_documents: boolean; // For background verification

  @Column({ type: 'boolean', default: false })
  requires_admin_approval: boolean; // For manual approval stages

  @Column({ type: 'json', nullable: true })
  auto_advance_conditions: any; // Conditions for auto-advancement

  @Column({ type: 'json', nullable: true })
  notification_templates: any; // Email templates for this stage

  @Column({ type: 'int', nullable: true })
  min_score_required: number; // Minimum score for quiz forms

  @Column({ type: 'int', nullable: true })
  max_duration_days: number; // Maximum days to complete this stage

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  @OneToMany(() => JobStageCompletion, (completion) => completion.job_stage)
  completions: JobStageCompletion[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;

  @BeforeInsert()
  generateJobStageId() {
    this.job_stage_id = generateUUID();
    this.cloned_at = new Date();
  }
}
