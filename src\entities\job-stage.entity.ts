import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  BeforeInsert,
} from 'typeorm';

import { generateUUID } from 'src/util';
import { Job } from './job.entity';
import { Stages } from './stage.entity';
import { Organization } from './organization.entity';
import { JobStageCompletion } from './job-stage-completion.entity';
import { StageActionBy } from 'src/util/enums';

@Entity()
export class JobStage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  job_stage_id: string;

  @ManyToOne(() => Job, (job) => job.job_stages, {
    eager: false,
    nullable: false,
  })
  @JoinColumn()
  job: Job;

  @ManyToOne(() => Stages, { eager: false, nullable: true })
  @JoinColumn()
  parent_stage: Stages;

  @Column({ type: 'int', default: 1 })
  sequence_order: number;

  @Column({ type: 'boolean', default: true })
  is_required: boolean;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  // Cloned stage data from parent stage at time of assignment
  @Column({ type: 'varchar' })
  title: string;

  @Column({ type: 'longtext', nullable: true })
  description: string;

  @Column({ type: 'json', nullable: true })
  forms_list: string[];

  @Column({
    type: 'enum',
    enum: Object.values(StageActionBy),
  })
  action_by: StageActionBy;

  // Version tracking
  @Column({ type: 'int', default: 1 })
  stage_version: number;

  @Column({ type: 'datetime', nullable: true })
  cloned_at: Date;

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  @OneToMany(
    () => JobStageCompletion,
    (completion) => completion.job_stage,
  )
  completions: JobStageCompletion[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;

  @BeforeInsert()
  generateJobStageId() {
    this.job_stage_id = generateUUID();
    this.cloned_at = new Date();
  }
}
