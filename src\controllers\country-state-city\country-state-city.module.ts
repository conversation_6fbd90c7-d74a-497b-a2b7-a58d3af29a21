import { Module } from '@nestjs/common';
import { AppsModule } from '../apps/apps.module';
import CountryStateCityController from './country-state-city.controller';
import CountryStateCityService from 'src/services/country-state-city.service';
import { UserService } from 'src/services/user.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/entities/user.entity';
import { OrganizationService } from 'src/services/organization.service';
import { Organization } from 'src/entities/organization.entity';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import {
  OrgFormsRepository,
  OrgFormsRepositorySchema,
} from 'src/entities/mongodb/orgformsrepository.entity';
import { MongooseModule } from '@nestjs/mongoose';
import {
  FormsRepository,
  FormsRepositorySchema,
} from 'src/entities/mongodb/formsrepository.entity';
import {
  FormValuesRepository,
  FormValuesRepositorySchema,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import {
  ClientsRepository,
  ClientsRepositorySchema,
} from 'src/entities/mongodb/clientsrepository.entity';
import { FormsRepositoryService } from 'src/services/formsrespository.service';
import { GlobalService } from 'src/services/global.service';
import { FormFieldsService } from 'src/services/formfields.service';
import {
  FormSectionsRepository,
  FormSectionsRepositorySchema,
} from 'src/entities/mongodb/form-sections.entity';
import { FormSectionsRepositoryService } from 'src/services/form-sections.service';
import {
  FormFields,
  FormFieldsSchema,
} from 'src/entities/mongodb/formfields.entity';
import { ConfigurationsService } from 'src/services/configurations.service';
import { Configurations } from 'src/entities/configurations.entity';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import { HttpModule } from '@nestjs/axios';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { SendGridService } from 'src/util/sendgrid.service';
import {
  ESignRequestLogsRepository,
  ESignRequestLogsSchema,
} from 'src/entities/mongodb/e-sign-request-log.entity';
import EncryptionService from 'src/services/encryption.service';
import {
  PrimaryFormsRepository,
  PrimaryFormsRepositorySchema,
} from 'src/entities/mongodb/primary-forms-repository.entity';
import {
  AppOrgFormsToggleRepository,
  AppOrgFormsToggleRepositorySchema,
} from 'src/entities/mongodb/app-org-forms-toggle.entity';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { FormValueService } from 'src/services/formvalues.service';
import { OnboardingEmployeeModule } from '../employee/onboarding-employee.module';
import { PaginationService } from 'src/util/pagination.service';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import { CaregiversModule } from '../caregivers/caregivers.module';
import { JobService } from 'src/services/job.service';
import { JobVersionService } from 'src/services/job-version.service';
import { JobStageService } from 'src/services/job-stage.service';
import { JobStageCompletion } from 'src/entities/job-stage-completion.entity';
import { JobStage } from 'src/entities/job-stage.entity';
import { JobVersion } from 'src/entities/job-version.entity';
import { Job } from 'src/entities/job.entity';
import { Stages } from 'src/entities/stage.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        User,
        Organization,
        Configurations,
        IndustryTypes,
        OrgAppConfiguration,
        OnboardingEmployee,
        IndustryAppProcess,
        Job,
        JobVersion,
        JobStage,
        JobStageCompletion,
        Stages,
      ],
      'mysql',
    ),
    CaregiversModule,
    MongooseModule.forFeature([
      { name: OrgFormsRepository.name, schema: OrgFormsRepositorySchema },
      { name: FormsRepository.name, schema: FormsRepositorySchema },
      { name: FormValuesRepository.name, schema: FormValuesRepositorySchema },
      { name: ClientsRepository.name, schema: ClientsRepositorySchema },
      { name: FormFields.name, schema: FormFieldsSchema },
      { name: ESignRequestLogsRepository.name, schema: ESignRequestLogsSchema },
      {
        name: PrimaryFormsRepository.name,
        schema: PrimaryFormsRepositorySchema,
      },
      {
        name: AppOrgFormsToggleRepository.name,
        schema: AppOrgFormsToggleRepositorySchema,
      },
      {
        name: FormSectionsRepository.name,
        schema: FormSectionsRepositorySchema,
      },
      {
        name: OnBoardEmpChecklistRepository.name,
        schema: OnBoardEmpChecklistRepositorySchema,
      },
      {
        name: OnBoardOrgChecklistRepository.name,
        schema: OnBoardOrgChecklistRepositorySchema,
      },
    ]),
    AppsModule,
    HttpModule,
    OnboardingEmployeeModule,
  ],
  controllers: [CountryStateCityController],
  providers: [
    CountryStateCityService,
    UserService,
    OrganizationService,
    OrgFormsRepositoryService,
    FormsRepositoryService,
    GlobalService,
    FormFieldsService,
    FormSectionsRepositoryService,
    ConfigurationsService,
    IndustryTypesService,
    SendGridService,
    ClientsRepositoryService,
    EncryptionService,
    OnboardingEmployeeService,
    FormValueService,
    PaginationService,
    OnBoardEmpChecklistService,
    OnBoardOrgChecklistService,
    JobService,
    JobVersionService,
    JobStageService,
  ],
  exports: [CountryStateCityService],
})
export class CountryStateCityModule {}
