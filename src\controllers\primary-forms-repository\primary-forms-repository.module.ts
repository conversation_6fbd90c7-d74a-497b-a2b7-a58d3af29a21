import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';

import {
  PrimaryFormsRepository,
  PrimaryFormsRepositorySchema,
} from 'src/entities/mongodb/primary-forms-repository.entity';
import PrimaryFormsRepositoryController from './primary-forms-repository.controller';
import { UserService } from 'src/services/user.service';
import { User } from 'src/entities/user.entity';
import { Apps } from 'src/entities/apps.entity';
import { Configurations } from 'src/entities/configurations.entity';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { IndustryTypes } from 'src/entities/industry-types.entity';
import {
  ClientsRepository,
  ClientsRepositorySchema,
} from 'src/entities/mongodb/clientsrepository.entity';
import {
  ESignRequestLogsRepository,
  ESignRequestLogsSchema,
} from 'src/entities/mongodb/e-sign-request-log.entity';
import {
  FormSectionsRepository,
  FormSectionsRepositorySchema,
} from 'src/entities/mongodb/form-sections.entity';
import {
  FormFields,
  FormFieldsSchema,
} from 'src/entities/mongodb/formfields.entity';
import { FormsRepository } from 'src/entities/mongodb/formsrepository.entity';
import {
  FormValuesRepository,
  FormValuesRepositorySchema,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import {
  OrgFormsRepository,
  OrgFormsRepositorySchema,
} from 'src/entities/mongodb/orgformsrepository.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { Organization } from 'src/entities/organization.entity';
import { AppsService } from 'src/services/apps.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import EncryptionService from 'src/services/encryption.service';
import { FormSectionsRepositoryService } from 'src/services/form-sections.service';
import { FormFieldsService } from 'src/services/formfields.service';
import { FormsRepositoryService } from 'src/services/formsrespository.service';
import { FormValueService } from 'src/services/formvalues.service';
import { GlobalService } from 'src/services/global.service';
import { IndustryAppProcessService } from 'src/services/industry-app-process.service';
import { IndustryTypesService } from 'src/services/industry-types.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { OrganizationService } from 'src/services/organization.service';
import { OrgFormsRepositoryService } from 'src/services/orgformsrepository.service';
import { SendGridService } from 'src/util/sendgrid.service';
import { ConfigurationsModule } from '../configurations/configurations.module';
import { OnboardingEmployeeModule } from '../employee/onboarding-employee.module';
import { PrimaryFormsRepositoryService } from 'src/services/primary-forms-repository.service';
import {
  AppOrgFormsToggleRepository,
  AppOrgFormsToggleRepositorySchema,
} from 'src/entities/mongodb/app-org-forms-toggle.entity';
import { PaginationService } from 'src/util/pagination.service';
import {
  OnBoardEmpChecklistRepository,
  OnBoardEmpChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-emp-checklist-repository.entity';
import {
  OnBoardOrgChecklistRepository,
  OnBoardOrgChecklistRepositorySchema,
} from 'src/entities/mongodb/onboard-org-checklist-repository.entity';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { CaregiversService } from 'src/services/caregivers.service';
import { Caregivers } from 'src/entities/caregivers.entity';
import { JobService } from 'src/services/job.service';
import { JobVersionService } from 'src/services/job-version.service';
import { JobStageService } from 'src/services/job-stage.service';
import { Job } from 'src/entities/job.entity';
import { Stages } from 'src/entities/stage.entity';
import { JobStageCompletion } from 'src/entities/job-stage-completion.entity';
import { JobStage } from 'src/entities/job-stage.entity';
import { JobVersion } from 'src/entities/job-version.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        User,
        Organization,
        Apps,
        IndustryTypes,
        IndustryAppProcess,
        OrgAppConfiguration,
        OnboardingEmployee,
        Configurations,
        Caregivers,
        Job,
        JobVersion,
        JobStage,
        JobStageCompletion,
        Stages,
      ],
      'mysql',
    ),
    MongooseModule.forFeature([
      {
        name: PrimaryFormsRepository.name,
        schema: PrimaryFormsRepositorySchema,
      },
      { name: ClientsRepository.name, schema: ClientsRepositorySchema },
      { name: OrgFormsRepository.name, schema: OrgFormsRepositorySchema },
      { name: FormsRepository.name, schema: OrgFormsRepositorySchema },
      { name: FormValuesRepository.name, schema: FormValuesRepositorySchema },
      { name: FormFields.name, schema: FormFieldsSchema },
      { name: ESignRequestLogsRepository.name, schema: ESignRequestLogsSchema },
      {
        name: AppOrgFormsToggleRepository.name,
        schema: AppOrgFormsToggleRepositorySchema,
      },

      {
        name: FormSectionsRepository.name,
        schema: FormSectionsRepositorySchema,
      },
      {
        name: OnBoardEmpChecklistRepository.name,
        schema: OnBoardEmpChecklistRepositorySchema,
      },
      {
        name: OnBoardOrgChecklistRepository.name,
        schema: OnBoardOrgChecklistRepositorySchema,
      },
    ]),
    OnboardingEmployeeModule,
    ConfigurationsModule,
  ],
  controllers: [PrimaryFormsRepositoryController],
  providers: [
    ClientsRepositoryService,
    UserService,
    OrganizationService,
    AppsService,
    OrgFormsRepositoryService,
    FormsRepositoryService,
    GlobalService,
    FormFieldsService,
    FormSectionsRepositoryService,
    IndustryTypesService,
    IndustryAppProcessService,
    OnboardingEmployeeService,
    FormValueService,
    SendGridService,
    EncryptionService,
    PrimaryFormsRepositoryService,
    PaginationService,
    OnBoardEmpChecklistService,
    OnBoardOrgChecklistService,
    CaregiversService,
    JobService,
    JobVersionService,
    JobStageService,
  ],
  exports: [],
})
export class PrimaryFormsRepositoryModule {}
