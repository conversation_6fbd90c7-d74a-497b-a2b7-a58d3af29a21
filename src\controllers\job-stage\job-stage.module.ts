import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import JobStageController from './job-stage.controller';
import { JobStageService } from 'src/services/job-stage.service';
import { JobStage } from 'src/entities/job-stage.entity';
import { JobStageCompletion } from 'src/entities/job-stage-completion.entity';
import { Job } from 'src/entities/job.entity';
import { Stages } from 'src/entities/stage.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { Organization } from 'src/entities/organization.entity';
import { User } from 'src/entities/user.entity';
import { OrganizationService } from 'src/services/organization.service';
import { JobService } from 'src/services/job.service';
import { JobVersionService } from 'src/services/job-version.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { JobVersion } from 'src/entities/job-version.entity';
import { OrganizationModule } from '../organization/organization.module';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import { Configurations } from 'src/entities/configurations.entity';
import { AppsService } from 'src/services/apps.service';
import { IndustryAppProcess } from 'src/entities/industry-app-process.entity';
import { FormValueService } from 'src/services/formvalues.service';
import { Apps } from 'src/entities/apps.entity';
import { IndustryAppProcessService } from 'src/services/industry-app-process.service';
import { PaginationService } from 'src/util/pagination.service';
import { ClientsRepositoryService } from 'src/services/clientsrepository.service';
import { OnBoardEmpChecklistService } from 'src/services/onboard-emp-checklist.service';
import { OnBoardOrgChecklistService } from 'src/services/onboard-org-checklist.service';
import { UserService } from 'src/services/user.service';

@Module({
  imports: [
    OrganizationModule,
    TypeOrmModule.forFeature(
      [
        JobStage,
        JobStageCompletion,
        Job,
        JobVersion,
        Stages,
        OnboardingEmployee,
        Organization,
        User,
        OrgAppConfiguration,
        Configurations,
        IndustryAppProcess,
        Apps,
      ],
      'mysql',
    ),
  ],
  exports: [JobStageService],
  providers: [
    JobStageService,
    OrganizationService,
    JobService,
    JobVersionService,
    AppsService,
    FormValueService,
    OnboardingEmployeeService,
    IndustryAppProcessService,
    PaginationService,
    ClientsRepositoryService,
    OnBoardEmpChecklistService,
    OnBoardOrgChecklistService,
    UserService,
  ],
  controllers: [JobStageController],
})
export class JobStageModule {}
