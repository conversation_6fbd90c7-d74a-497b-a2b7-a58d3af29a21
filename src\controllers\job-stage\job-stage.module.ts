import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import JobStageController from './job-stage.controller';
import { JobStageService } from 'src/services/job-stage.service';

import { JobStageCompletion } from 'src/entities/job-stage-completion.entity';
import { Job } from 'src/entities/job.entity';
import { Stages } from 'src/entities/stage.entity';
import { OnboardingEmployee } from 'src/entities/onboarding-employee.entity';
import { Organization } from 'src/entities/organization.entity';
import { User } from 'src/entities/user.entity';
import { OrganizationService } from 'src/services/organization.service';
import { JobService } from 'src/services/job.service';
import { JobVersionService } from 'src/services/job-version.service';
import { OnboardingEmployeeService } from 'src/services/onboarding-employee.service';
import { JobVersion } from 'src/entities/job-version.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        JobStage,
        JobStageCompletion,
        Job,
        JobVersion,
        Stages,
        OnboardingEmployee,
        Organization,
        User,
      ],
      'mysql',
    ),
  ],
  exports: [JobStageService],
  providers: [
    JobStageService,
    OrganizationService,
    JobService,
    JobVersionService,
    OnboardingEmployeeService,
  ],
  controllers: [JobStageController],
})
export class JobStageModule {}
