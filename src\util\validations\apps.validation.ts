import { Injectable } from '@nestjs/common';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { In } from 'typeorm';

import { BaseValidator } from './base.validator';
import { AppsService } from 'src/services/apps.service';

@ValidatorConstraint({ name: 'AppsExist', async: true })
@Injectable()
export class AppsExistRule implements ValidatorConstraintInterface {
  constructor(private appsService: AppsService) {}
  private value_type: any;

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    try {
      this.value_type = typeof value;
      if (this.value_type === 'object') {
        if (value.length) {
          const data = await this.appsService.list({
            app_id: In(value),
            status: true,
          });
          return data.length === value.length ? true : false;
        }
        return false;
      } else {
        const data = await this.appsService.findOne(
          {
            app_id: value,
            status: true,
          },
          false,
        );
        return data ? true : false;
      }
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  defaultMessage() {
    return `Please select the existed App only`;
  }
}

export function AppsExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'AppsExist',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: AppsExistRule,
    });
  };
}

@ValidatorConstraint({ name: 'AppNameExist', async: true })
@Injectable()
export class AppNameExistRule implements ValidatorConstraintInterface {
  constructor(private appsService: AppsService) {}
  private value_type: any;

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    try {
      const data = await this.appsService.findOne(
        {
          name: value,
          status: true,
        },
        false,
      );

      return data ? false : true;
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  defaultMessage() {
    return `Application name already exist!`;
  }
}

export function AppNameExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'AppNameExist',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: AppNameExistRule,
    });
  };
}

@ValidatorConstraint({ name: 'OrgAppsExist', async: true })
@Injectable()
export class OrgAppsExistRule
  extends BaseValidator
  implements ValidatorConstraintInterface
{
  constructor(private appsService: AppsService) {
    super();
  }
  private value_type: any;

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    try {
      this.value_type = typeof value;
      if (this.value_type === 'object') {
        if (value.length) {
          const data = await this.appsService.list(
            {
              app_id: In(value),
              status: true,
              organizations: {
                organization_id: this.getOrganizationId(),
              },
            },
            {
              app_id: true,
              orgAppConfiguration: false,
            },
          );
          return data.length === value.length ? true : false;
        }
        return false;
      } else {
        const data = await this.appsService.findOne(
          {
            app_id: value,
            status: true,
          },
          false,
          {
            ...this.appsService.select,
            id: true,
            organizations: {
              organization_id: true,
            },
          },
          {
            organizations: true,
          },
        );
        return data
          ? data.organizations.some(
              (organization) =>
                organization.organization_id === this.getOrganizationId(),
            )
          : false;
      }
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  getOrganizationId() {
    return this.getRequestContext().user['organization_id'];
  }

  defaultMessage() {
    return `Please select the existed App only`;
  }
}

export function OrgAppsExists(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'OrgAppsExist',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: OrgAppsExistRule,
    });
  };
}
